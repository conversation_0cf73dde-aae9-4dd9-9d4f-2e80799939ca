{"version": 3, "file": "security.js", "sourceRoot": "", "sources": ["security.ts"], "names": [], "mappings": ";AAAA,YAAY,CAAC;;;AAwBb,8CAIC;AAcD,oCA8BC;AAGD,4CAsBC;AAGD,kDAIC;AAuBD,wDAGC;AAGD,4CAaC;AAGD,4CAqBC;AAxKD,wCAAwC;AAExC,sDAAsD;AACzC,QAAA,UAAU,GAAG;IACxB,aAAa,EAAE,CAAC,QAAQ,CAAC;IACzB,YAAY,EAAE,CAAC,QAAQ,EAAE,iBAAiB,CAAC,EAAE,mCAAmC;IAChF,WAAW,EAAE,CAAC,QAAQ,EAAE,iBAAiB,CAAC,EAAE,6CAA6C;IACzF,SAAS,EAAE,CAAC,QAAQ,EAAE,OAAO,EAAE,OAAO,CAAC;IACvC,UAAU,EAAE,CAAC,QAAQ,CAAC;IACtB,aAAa,EAAE,CAAC,QAAQ,CAAC;IACzB,WAAW,EAAE,CAAC,QAAQ,CAAC;IACvB,YAAY,EAAE,CAAC,QAAQ,CAAC;IACxB,WAAW,EAAE,CAAC,QAAQ,CAAC;IACvB,YAAY,EAAE,CAAC,QAAQ,CAAC;IACxB,WAAW,EAAE,CAAC,QAAQ,CAAC;IACvB,UAAU,EAAE,CAAC,QAAQ,CAAC;IACtB,aAAa,EAAE,CAAC,QAAQ,CAAC;IACzB,iBAAiB,EAAE,CAAC,QAAQ,CAAC;IAC7B,2BAA2B,EAAE,EAAE;CAChC,CAAC;AAEF,6CAA6C;AAC7C,SAAgB,iBAAiB;IAC/B,OAAO,MAAM,CAAC,OAAO,CAAC,kBAAU,CAAC;SAC9B,GAAG,CAAC,CAAC,CAAC,SAAS,EAAE,OAAO,CAAC,EAAE,EAAE,CAAC,GAAG,SAAS,IAAI,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;SAClE,IAAI,CAAC,IAAI,CAAC,CAAC;AAChB,CAAC;AAED,2CAA2C;AAC9B,QAAA,gBAAgB,GAAG;IAC9B,yBAAyB,EAAE,iBAAiB,EAAE;IAC9C,wBAAwB,EAAE,SAAS;IACnC,iBAAiB,EAAE,MAAM;IACzB,kBAAkB,EAAE,eAAe;IACnC,iBAAiB,EAAE,iCAAiC;IACpD,oBAAoB,EAAE,sDAAsD;IAC5E,2BAA2B,EAAE,qCAAqC;CACnE,CAAC;AAEF,yCAAyC;AACzC,SAAgB,YAAY,CAAC,GAAW,EAAE,QAAiB,KAAK;IAC9D,IAAI,CAAC;QACH,MAAM,SAAS,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;QAE/B,sCAAsC;QACtC,IAAI,SAAS,CAAC,QAAQ,KAAK,OAAO,EAAE,CAAC;YACnC,OAAO,IAAI,CAAC;QACd,CAAC;QAED,yCAAyC;QACzC,IAAI,SAAS,CAAC,QAAQ,KAAK,MAAM,EAAE,CAAC;YAClC,OAAO,IAAI,CAAC;QACd,CAAC;QAED,gCAAgC;QAChC,IAAI,SAAS,CAAC,QAAQ,KAAK,WAAW,EAAE,CAAC;YACvC,OAAO,IAAI,CAAC;QACd,CAAC;QAED,iCAAiC;QACjC,IAAI,KAAK,IAAI,SAAS,CAAC,QAAQ,KAAK,WAAW,IAAI,SAAS,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;YAC7E,OAAO,IAAI,CAAC;QACd,CAAC;QAED,uBAAuB;QACvB,OAAO,KAAK,CAAC;IACf,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,qBAAqB;QACrB,OAAO,KAAK,CAAC;IACf,CAAC;AACH,CAAC;AAED,qDAAqD;AACrD,SAAgB,gBAAgB,CAAC,QAAgB;IAC/C,IAAI,OAAO,QAAQ,KAAK,QAAQ;QAAE,OAAO,KAAK,CAAC;IAE/C,qBAAqB;IACrB,MAAM,UAAU,GAAG,QAAQ,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;IAEhD,yCAAyC;IACzC,IAAI,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,UAAU,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;QAC9D,OAAO,KAAK,CAAC;IACf,CAAC;IAED,gDAAgD;IAChD,IAAI,UAAU,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC;QAChE,OAAO,KAAK,CAAC;IACf,CAAC;IAED,uBAAuB;IACvB,IAAI,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;QAC9B,OAAO,KAAK,CAAC;IACf,CAAC;IAED,OAAO,IAAI,CAAC;AACd,CAAC;AAED,kCAAkC;AAClC,SAAgB,mBAAmB,CAAC,SAAiB,EAAE;IACrD,MAAM,KAAK,GAAG,IAAI,UAAU,CAAC,MAAM,CAAC,CAAC;IACrC,MAAM,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;IAC9B,OAAO,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AAChF,CAAC;AAED,gCAAgC;AACnB,QAAA,cAAc,GAAG;IAC5B,UAAU,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,aAAa;IACzC,mBAAmB,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI,EAAE,YAAY;IAChD,wBAAwB,EAAE,CAAC;CAC5B,CAAC;AAEF,8BAA8B;AACjB,QAAA,iBAAiB,GAAG;IAC/B,YAAY,EAAE;QACZ,YAAY,EAAE,CAAC;QACf,SAAS,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,aAAa;QACxC,UAAU,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI,CAAE,+BAA+B;KAC3D;IACD,YAAY,EAAE;QACZ,YAAY,EAAE,GAAG;QACjB,SAAS,EAAE,EAAE,GAAG,IAAI,CAAC,WAAW;KACjC;CACF,CAAC;AAEF,qCAAqC;AACrC,SAAgB,sBAAsB,CAAC,MAAc;IACnD,MAAM,cAAc,GAAG,CAAC,KAAK,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC;IACnD,OAAO,cAAc,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;AACzC,CAAC;AAED,yBAAyB;AACzB,SAAgB,gBAAgB,CAAC,KAAa,EAAE,UAAe,EAAE;IAC/D,MAAM,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;IAC3C,MAAM,QAAQ,GAAG;QACf,SAAS;QACT,KAAK;QACL,OAAO;QACP,SAAS,EAAE,OAAO,SAAS,KAAK,WAAW,CAAC,CAAC,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS;KAC9E,CAAC;IAEF,OAAO,CAAC,IAAI,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC;IAErC,8EAA8E;IAC9E,qCAAqC;AACvC,CAAC;AAED,mCAAmC;AACnC,SAAgB,gBAAgB,CAAC,QAAgB;IAC/C,IAAI,OAAO,QAAQ,KAAK,QAAQ;QAAE,OAAO,KAAK,CAAC;IAE/C,iCAAiC;IACjC,MAAM,cAAc,GAAG,oBAAoB,CAAC;IAC5C,IAAI,cAAc,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;QAClC,OAAO,KAAK,CAAC;IACf,CAAC;IAED,qCAAqC;IACrC,MAAM,aAAa,GAAG,wCAAwC,CAAC;IAC/D,IAAI,aAAa,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;QACjC,OAAO,KAAK,CAAC;IACf,CAAC;IAED,eAAe;IACf,IAAI,QAAQ,CAAC,MAAM,GAAG,GAAG,EAAE,CAAC;QAC1B,OAAO,KAAK,CAAC;IACf,CAAC;IAED,OAAO,IAAI,CAAC;AACd,CAAC"}