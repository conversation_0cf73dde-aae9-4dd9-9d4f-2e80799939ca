import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { createProject, createJunction, createSegment } from '../db/dao';
import { Auth } from '../core/auth/AuthService';
import { DeviceVault } from '../lib/vault';
import { ulid } from '../lib/ids';
import { validateFilePath } from '../lib/security';
import { sanitizeHtml } from '../lib/inputValidation';

describe('[phase5] Security Integration Tests', () => {
  describe('SQL Injection Prevention', () => {
    it('prevents SQL injection in project names', async () => {
      const maliciousName = "'; DROP TABLE projects; --";
      
      await expect(createProject(maliciousName, 'imperial', 'residential'))
        .rejects.toThrow('invalid characters');
    });

    it('prevents SQL injection in project descriptions', async () => {
      const maliciousDesc = "'; DELETE FROM projects WHERE 1=1; --";
      
      await expect(createProject('Test Project', 'imperial', 'residential', maliciousDesc))
        .rejects.toThrow(); // Should be caught by input validation
    });

    it('safely handles special characters in valid inputs', async () => {
      const projectName = "Project with 'quotes' and \"double quotes\"";
      
      // This should be rejected due to dangerous characters
      await expect(createProject(projectName, 'imperial', 'residential'))
        .rejects.toThrow('invalid characters');
    });
  });

  describe('XSS Prevention', () => {
    it('sanitizes HTML in project names', async () => {
      const xssName = '<script>alert("xss")</script>Project';
      
      await expect(createProject(xssName, 'imperial', 'residential'))
        .rejects.toThrow('invalid characters');
    });

    it('rejects HTML in project descriptions for security', async () => {
      const xssDesc = 'Description with <img src="x" onerror="alert(1)">';

      // Should be rejected due to dangerous characters
      await expect(createProject('Safe Project', 'imperial', 'residential', xssDesc))
        .rejects.toThrow('invalid characters');
    });

    it('rejects javascript protocols in descriptions', async () => {
      const jsDesc = 'Click here: javascript:alert(1)';

      // Should be rejected due to dangerous characters
      await expect(createProject('Test Project', 'imperial', 'residential', jsDesc))
        .rejects.toThrow('invalid characters');
    });
  });

  describe('Input Validation', () => {
    it('validates project name length limits', async () => {
      const longName = 'a'.repeat(101); // Exceeds 100 character limit
      
      await expect(createProject(longName, 'imperial', 'residential'))
        .rejects.toThrow('100 characters or less');
    });

    it('validates project description length limits', async () => {
      const longDesc = 'a'.repeat(501); // Exceeds 500 character limit
      
      await expect(createProject('Test Project', 'imperial', 'residential', longDesc))
        .rejects.toThrow('500 characters or less');
    });

    it('validates numeric inputs for junctions', async () => {
      const projectId = ulid();
      
      await expect(createJunction(projectId, 'supply', NaN, 20, 0))
        .rejects.toThrow('valid number');
        
      await expect(createJunction(projectId, 'supply', 10, Infinity, 0))
        .rejects.toThrow('valid number');
    });

    it('validates ULID format for IDs', async () => {
      await expect(createJunction('invalid-id', 'supply', 10, 20, 0))
        .rejects.toThrow('Invalid project ID');
        
      await expect(createSegment('invalid-id', 'round', ulid(), ulid(), 0.1, 0.3, 10))
        .rejects.toThrow('Invalid project ID');
    });
  });

  describe('Authentication Security', () => {
    const testAccountId = ulid();

    beforeEach(async () => {
      // Clean up any existing sessions
      Auth.lock();
    });

    it('validates PIN format', async () => {
      await expect(Auth.setPin(testAccountId, '123'))
        .rejects.toThrow('at least 6 digits');
        
      await expect(Auth.setPin(testAccountId, '***********'))
        .rejects.toThrow('10 digits or less');
        
      await expect(Auth.setPin(testAccountId, 'abc123'))
        .rejects.toThrow('at least 6 digits'); // 'abc123' sanitizes to '123' which is too short
    });

    it('validates account ID format', async () => {
      await expect(Auth.setPin('invalid-id', '123456'))
        .rejects.toThrow('Invalid account ID');
        
      await expect(Auth.verifyPin('invalid-id', '123456'))
        .rejects.toThrow('Invalid account ID');
    });

    it('enforces rate limiting on PIN attempts', async () => {
      // This test would need to be implemented with proper rate limiting
      // For now, we'll just verify the structure exists
      expect(Auth.verifyPin).toBeDefined();
    });
  });

  describe('Vault Security', () => {
    it('validates project ID for vault operations', async () => {
      const testData = { test: 'data' };
      
      await expect(DeviceVault.createSnapshot('invalid-id', testData))
        .rejects.toThrow('Invalid project ID');
    });

    it('validates data format for vault storage', async () => {
      const projectId = ulid();
      
      // Create circular reference to test JSON validation
      const circularData: any = { test: 'data' };
      circularData.circular = circularData;
      
      await expect(DeviceVault.createSnapshot(projectId, circularData))
        .rejects.toThrow('Invalid data');
    });

    it('validates snapshot ID format for retrieval', async () => {
      await expect(DeviceVault.getSnapshot(''))
        .rejects.toThrow('Invalid snapshot ID');
        
      await expect(DeviceVault.getSnapshot('   '))
        .rejects.toThrow('Invalid snapshot ID');
    });
  });

  describe('File Path Security', () => {
    it('prevents directory traversal in file paths', () => {
      expect(validateFilePath('../../../etc/passwd')).toBe(false);
      expect(validateFilePath('..\\..\\windows\\system32')).toBe(false);
      expect(validateFilePath('valid/path/file.txt')).toBe(true);
    });
  });

  describe('Data Sanitization', () => {
    it('removes dangerous HTML content', () => {
      expect(sanitizeHtml('<script>alert("xss")</script>')).toBe('');
      expect(sanitizeHtml('<img src="x" onerror="alert(1)">')).toBe('');
      expect(sanitizeHtml('javascript:alert(1)')).toBe('alert(1)');
    });

    it('preserves safe content', () => {
      expect(sanitizeHtml('Safe text content')).toBe('Safe text content');
      expect(sanitizeHtml('Numbers 123 and symbols !@#')).toBe('Numbers 123 and symbols !@#');
    });
  });
});
