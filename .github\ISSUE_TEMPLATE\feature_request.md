---
name: Feature Request
about: Suggest a new feature for SizeWise
title: '[FEATURE] '
labels: ['enhancement']
assignees: ''
---

## Goal
<!-- What we're building and why -->

## Acceptance Tests (dev must implement)

### Unit Tests
- [ ] `<module>` validates inputs and rejects NaNs
- [ ] `<module>` handles edge cases correctly
- [ ] `<module>` returns expected outputs for valid inputs

### Integration Tests  
- [ ] DAO blocks free-tier limits and returns clear errors
- [ ] Vault encryption/decryption round-trip works correctly
- [ ] Database operations maintain data integrity

### E2E Tests (Playwright)
- [ ] User can `<complete the workflow>` 
- [ ] Data persists after page reload (offline capability)
- [ ] Error states are handled gracefully

## Acceptance Criteria
<!-- Specific, measurable requirements -->
- [ ] Feature works as described
- [ ] Performance meets requirements (if applicable)
- [ ] Accessibility standards met
- [ ] Mobile/tablet compatibility (no mobile version needed)
- [ ] Offline functionality maintained

## Technical Requirements
- [ ] TypeScript types defined
- [ ] Error handling implemented
- [ ] Loading states handled
- [ ] Responsive design (tablet+)

## Out of Scope
<!-- What this feature explicitly does NOT include -->

## Design Mockups
<!-- Attach any UI mockups or wireframes -->

## Additional Context
<!-- Any other context, screenshots, or examples -->
