{"compilerOptions": {"target": "ES2022", "lib": ["dom", "es2022"], "jsx": "preserve", "module": "esnext", "moduleResolution": "bundler", "strict": true, "baseUrl": ".", "paths": {"@/*": ["*"]}, "allowJs": true, "skipLibCheck": true, "noEmit": true, "incremental": true, "esModuleInterop": true, "resolveJsonModule": true, "isolatedModules": true, "plugins": [{"name": "next"}]}, "include": ["app", "components", "db", "lib", "public/sw.js", ".next/types/**/*.ts", "types/**/*.d.ts"], "exclude": ["node_modules"]}