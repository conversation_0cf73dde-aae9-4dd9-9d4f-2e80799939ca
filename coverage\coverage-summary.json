{"total": {"lines": {"total": 1142, "covered": 551, "skipped": 0, "pct": 48.24}, "statements": {"total": 1142, "covered": 551, "skipped": 0, "pct": 48.24}, "functions": {"total": 78, "covered": 52, "skipped": 0, "pct": 66.66}, "branches": {"total": 196, "covered": 161, "skipped": 0, "pct": 82.14}, "branchesTrue": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "/Users/<USER>/Documents/SizeWise_Offline_App/core/auth/AuthService.ts": {"lines": {"total": 193, "covered": 46, "skipped": 0, "pct": 23.83}, "functions": {"total": 14, "covered": 3, "skipped": 0, "pct": 21.42}, "statements": {"total": 193, "covered": 46, "skipped": 0, "pct": 23.83}, "branches": {"total": 12, "covered": 5, "skipped": 0, "pct": 41.66}}, "/Users/<USER>/Documents/SizeWise_Offline_App/core/auth/idleLock.ts": {"lines": {"total": 16, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 1, "skipped": 0, "pct": 100}, "statements": {"total": 16, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 1, "skipped": 0, "pct": 100}}, "/Users/<USER>/Documents/SizeWise_Offline_App/db/dao.ts": {"lines": {"total": 218, "covered": 165, "skipped": 0, "pct": 75.68}, "functions": {"total": 17, "covered": 15, "skipped": 0, "pct": 88.23}, "statements": {"total": 218, "covered": 165, "skipped": 0, "pct": 75.68}, "branches": {"total": 52, "covered": 37, "skipped": 0, "pct": 71.15}}, "/Users/<USER>/Documents/SizeWise_Offline_App/db/migrations.ts": {"lines": {"total": 36, "covered": 33, "skipped": 0, "pct": 91.66}, "functions": {"total": 2, "covered": 2, "skipped": 0, "pct": 100}, "statements": {"total": 36, "covered": 33, "skipped": 0, "pct": 91.66}, "branches": {"total": 11, "covered": 9, "skipped": 0, "pct": 81.81}}, "/Users/<USER>/Documents/SizeWise_Offline_App/db/openDb.ts": {"lines": {"total": 17, "covered": 1, "skipped": 0, "pct": 5.88}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 17, "covered": 1, "skipped": 0, "pct": 5.88}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "/Users/<USER>/Documents/SizeWise_Offline_App/lib/featureFlags.ts": {"lines": {"total": 45, "covered": 41, "skipped": 0, "pct": 91.11}, "functions": {"total": 8, "covered": 6, "skipped": 0, "pct": 75}, "statements": {"total": 45, "covered": 41, "skipped": 0, "pct": 91.11}, "branches": {"total": 17, "covered": 17, "skipped": 0, "pct": 100}}, "/Users/<USER>/Documents/SizeWise_Offline_App/lib/ids.ts": {"lines": {"total": 6, "covered": 6, "skipped": 0, "pct": 100}, "functions": {"total": 1, "covered": 1, "skipped": 0, "pct": 100}, "statements": {"total": 6, "covered": 6, "skipped": 0, "pct": 100}, "branches": {"total": 2, "covered": 2, "skipped": 0, "pct": 100}}, "/Users/<USER>/Documents/SizeWise_Offline_App/lib/inputValidation.ts": {"lines": {"total": 184, "covered": 169, "skipped": 0, "pct": 91.84}, "functions": {"total": 14, "covered": 13, "skipped": 0, "pct": 92.85}, "statements": {"total": 184, "covered": 169, "skipped": 0, "pct": 91.84}, "branches": {"total": 69, "covered": 67, "skipped": 0, "pct": 97.1}}, "/Users/<USER>/Documents/SizeWise_Offline_App/lib/licensing.ts": {"lines": {"total": 49, "covered": 49, "skipped": 0, "pct": 100}, "functions": {"total": 6, "covered": 6, "skipped": 0, "pct": 100}, "statements": {"total": 49, "covered": 49, "skipped": 0, "pct": 100}, "branches": {"total": 21, "covered": 17, "skipped": 0, "pct": 80.95}}, "/Users/<USER>/Documents/SizeWise_Offline_App/lib/security.ts": {"lines": {"total": 115, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 1, "skipped": 0, "pct": 100}, "statements": {"total": 115, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 1, "skipped": 0, "pct": 100}}, "/Users/<USER>/Documents/SizeWise_Offline_App/lib/sw-client.ts": {"lines": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 1, "skipped": 0, "pct": 100}, "statements": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 1, "skipped": 0, "pct": 100}}, "/Users/<USER>/Documents/SizeWise_Offline_App/lib/units.ts": {"lines": {"total": 3, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 3, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "/Users/<USER>/Documents/SizeWise_Offline_App/lib/vault.ts": {"lines": {"total": 259, "covered": 41, "skipped": 0, "pct": 15.83}, "functions": {"total": 11, "covered": 3, "skipped": 0, "pct": 27.27}, "statements": {"total": 259, "covered": 41, "skipped": 0, "pct": 15.83}, "branches": {"total": 8, "covered": 4, "skipped": 0, "pct": 50}}}