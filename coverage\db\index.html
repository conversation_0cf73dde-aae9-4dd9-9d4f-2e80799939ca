
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for db</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../prettify.css" />
    <link rel="stylesheet" href="../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../index.html">All files</a> db</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">73.43% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>199/271</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">73.01% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>46/63</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">85% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>17/20</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">73.43% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>199/271</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line medium'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file medium" data-value="dao.ts"><a href="dao.ts.html">dao.ts</a></td>
	<td data-value="75.68" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 75%"></div><div class="cover-empty" style="width: 25%"></div></div>
	</td>
	<td data-value="75.68" class="pct medium">75.68%</td>
	<td data-value="218" class="abs medium">165/218</td>
	<td data-value="71.15" class="pct medium">71.15%</td>
	<td data-value="52" class="abs medium">37/52</td>
	<td data-value="88.23" class="pct high">88.23%</td>
	<td data-value="17" class="abs high">15/17</td>
	<td data-value="75.68" class="pct medium">75.68%</td>
	<td data-value="218" class="abs medium">165/218</td>
	</tr>

<tr>
	<td class="file high" data-value="migrations.ts"><a href="migrations.ts.html">migrations.ts</a></td>
	<td data-value="91.66" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 91%"></div><div class="cover-empty" style="width: 9%"></div></div>
	</td>
	<td data-value="91.66" class="pct high">91.66%</td>
	<td data-value="36" class="abs high">33/36</td>
	<td data-value="81.81" class="pct high">81.81%</td>
	<td data-value="11" class="abs high">9/11</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="2" class="abs high">2/2</td>
	<td data-value="91.66" class="pct high">91.66%</td>
	<td data-value="36" class="abs high">33/36</td>
	</tr>

<tr>
	<td class="file low" data-value="openDb.ts"><a href="openDb.ts.html">openDb.ts</a></td>
	<td data-value="5.88" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 5%"></div><div class="cover-empty" style="width: 95%"></div></div>
	</td>
	<td data-value="5.88" class="pct low">5.88%</td>
	<td data-value="17" class="abs low">1/17</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="1" class="abs low">0/1</td>
	<td data-value="5.88" class="pct low">5.88%</td>
	<td data-value="17" class="abs low">1/17</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-08-12T18:39:57.821Z
            </div>
        <script src="../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../sorter.js"></script>
        <script src="../block-navigation.js"></script>
    </body>
</html>
    