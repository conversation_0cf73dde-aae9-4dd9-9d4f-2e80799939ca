{"/Users/<USER>/Documents/SizeWise_Offline_App/core/auth/AuthService.ts": {"path": "/Users/<USER>/Documents/SizeWise_Offline_App/core/auth/AuthService.ts", "all": false, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 40}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 45}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 36}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 82}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 109}}, "9": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": 41}}, "10": {"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 125}}, "13": {"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 23}}, "14": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 22}}, "15": {"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 27}}, "16": {"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 16}}, "17": {"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 12}}, "18": {"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 1}}, "20": {"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 58}}, "22": {"start": {"line": 23, "column": 0}, "end": {"line": 23, "column": 35}}, "23": {"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 75}}, "24": {"start": {"line": 25, "column": 0}, "end": {"line": 25, "column": 1}}, "26": {"start": {"line": 27, "column": 0}, "end": {"line": 27, "column": 82}}, "27": {"start": {"line": 28, "column": 0}, "end": {"line": 28, "column": 31}}, "28": {"start": {"line": 29, "column": 0}, "end": {"line": 29, "column": 52}}, "29": {"start": {"line": 30, "column": 0}, "end": {"line": 30, "column": 10}}, "30": {"start": {"line": 31, "column": 0}, "end": {"line": 31, "column": 20}}, "31": {"start": {"line": 32, "column": 0}, "end": {"line": 32, "column": 23}}, "32": {"start": {"line": 33, "column": 0}, "end": {"line": 33, "column": 10}}, "33": {"start": {"line": 34, "column": 0}, "end": {"line": 34, "column": 18}}, "34": {"start": {"line": 35, "column": 0}, "end": {"line": 35, "column": 3}}, "35": {"start": {"line": 36, "column": 0}, "end": {"line": 36, "column": 46}}, "36": {"start": {"line": 37, "column": 0}, "end": {"line": 37, "column": 115}}, "37": {"start": {"line": 38, "column": 0}, "end": {"line": 38, "column": 16}}, "38": {"start": {"line": 39, "column": 0}, "end": {"line": 39, "column": 28}}, "39": {"start": {"line": 40, "column": 0}, "end": {"line": 40, "column": 3}}, "40": {"start": {"line": 41, "column": 0}, "end": {"line": 41, "column": 29}}, "41": {"start": {"line": 42, "column": 0}, "end": {"line": 42, "column": 1}}, "43": {"start": {"line": 44, "column": 0}, "end": {"line": 44, "column": 65}}, "44": {"start": {"line": 45, "column": 0}, "end": {"line": 45, "column": 41}}, "45": {"start": {"line": 46, "column": 0}, "end": {"line": 46, "column": 14}}, "46": {"start": {"line": 47, "column": 0}, "end": {"line": 47, "column": 56}}, "47": {"start": {"line": 48, "column": 0}, "end": {"line": 48, "column": 19}}, "48": {"start": {"line": 49, "column": 0}, "end": {"line": 49, "column": 1}}, "50": {"start": {"line": 51, "column": 0}, "end": {"line": 51, "column": 21}}, "51": {"start": {"line": 52, "column": 0}, "end": {"line": 52, "column": 47}}, "52": {"start": {"line": 53, "column": 0}, "end": {"line": 53, "column": 19}}, "53": {"start": {"line": 54, "column": 0}, "end": {"line": 54, "column": 36}}, "54": {"start": {"line": 55, "column": 0}, "end": {"line": 55, "column": 70}}, "55": {"start": {"line": 56, "column": 0}, "end": {"line": 56, "column": 33}}, "56": {"start": {"line": 57, "column": 0}, "end": {"line": 57, "column": 21}}, "57": {"start": {"line": 58, "column": 0}, "end": {"line": 58, "column": 26}}, "58": {"start": {"line": 59, "column": 0}, "end": {"line": 59, "column": 102}}, "59": {"start": {"line": 60, "column": 0}, "end": {"line": 60, "column": 13}}, "60": {"start": {"line": 61, "column": 0}, "end": {"line": 61, "column": 4}}, "62": {"start": {"line": 63, "column": 0}, "end": {"line": 63, "column": 63}}, "64": {"start": {"line": 65, "column": 0}, "end": {"line": 65, "column": 56}}, "65": {"start": {"line": 66, "column": 0}, "end": {"line": 66, "column": 39}}, "66": {"start": {"line": 67, "column": 0}, "end": {"line": 67, "column": 74}}, "67": {"start": {"line": 68, "column": 0}, "end": {"line": 68, "column": 5}}, "69": {"start": {"line": 70, "column": 0}, "end": {"line": 70, "column": 43}}, "70": {"start": {"line": 71, "column": 0}, "end": {"line": 71, "column": 33}}, "71": {"start": {"line": 72, "column": 0}, "end": {"line": 72, "column": 43}}, "72": {"start": {"line": 73, "column": 0}, "end": {"line": 73, "column": 5}}, "74": {"start": {"line": 75, "column": 0}, "end": {"line": 75, "column": 36}}, "75": {"start": {"line": 76, "column": 0}, "end": {"line": 76, "column": 80}}, "76": {"start": {"line": 77, "column": 0}, "end": {"line": 77, "column": 68}}, "77": {"start": {"line": 78, "column": 0}, "end": {"line": 78, "column": 26}}, "78": {"start": {"line": 79, "column": 0}, "end": {"line": 79, "column": 138}}, "79": {"start": {"line": 80, "column": 0}, "end": {"line": 80, "column": 158}}, "80": {"start": {"line": 81, "column": 0}, "end": {"line": 81, "column": 4}}, "82": {"start": {"line": 83, "column": 0}, "end": {"line": 83, "column": 69}}, "84": {"start": {"line": 85, "column": 0}, "end": {"line": 85, "column": 56}}, "85": {"start": {"line": 86, "column": 0}, "end": {"line": 86, "column": 39}}, "86": {"start": {"line": 87, "column": 0}, "end": {"line": 87, "column": 94}}, "87": {"start": {"line": 88, "column": 0}, "end": {"line": 88, "column": 74}}, "88": {"start": {"line": 89, "column": 0}, "end": {"line": 89, "column": 5}}, "90": {"start": {"line": 91, "column": 0}, "end": {"line": 91, "column": 43}}, "91": {"start": {"line": 92, "column": 0}, "end": {"line": 92, "column": 33}}, "92": {"start": {"line": 93, "column": 0}, "end": {"line": 93, "column": 119}}, "93": {"start": {"line": 94, "column": 0}, "end": {"line": 94, "column": 43}}, "94": {"start": {"line": 95, "column": 0}, "end": {"line": 95, "column": 5}}, "97": {"start": {"line": 98, "column": 0}, "end": {"line": 98, "column": 68}}, "98": {"start": {"line": 99, "column": 0}, "end": {"line": 99, "column": 97}}, "99": {"start": {"line": 100, "column": 0}, "end": {"line": 100, "column": 72}}, "100": {"start": {"line": 101, "column": 0}, "end": {"line": 101, "column": 5}}, "102": {"start": {"line": 103, "column": 0}, "end": {"line": 103, "column": 36}}, "103": {"start": {"line": 104, "column": 0}, "end": {"line": 104, "column": 25}}, "104": {"start": {"line": 105, "column": 0}, "end": {"line": 105, "column": 13}}, "105": {"start": {"line": 106, "column": 0}, "end": {"line": 106, "column": 104}}, "106": {"start": {"line": 107, "column": 0}, "end": {"line": 107, "column": 44}}, "107": {"start": {"line": 108, "column": 0}, "end": {"line": 108, "column": 24}}, "108": {"start": {"line": 109, "column": 0}, "end": {"line": 109, "column": 39}}, "109": {"start": {"line": 110, "column": 0}, "end": {"line": 110, "column": 6}}, "110": {"start": {"line": 111, "column": 0}, "end": {"line": 111, "column": 52}}, "111": {"start": {"line": 112, "column": 0}, "end": {"line": 112, "column": 23}}, "112": {"start": {"line": 113, "column": 0}, "end": {"line": 113, "column": 100}}, "113": {"start": {"line": 114, "column": 0}, "end": {"line": 114, "column": 103}}, "115": {"start": {"line": 116, "column": 0}, "end": {"line": 116, "column": 26}}, "116": {"start": {"line": 117, "column": 0}, "end": {"line": 117, "column": 79}}, "117": {"start": {"line": 118, "column": 0}, "end": {"line": 118, "column": 64}}, "118": {"start": {"line": 119, "column": 0}, "end": {"line": 119, "column": 54}}, "119": {"start": {"line": 120, "column": 0}, "end": {"line": 120, "column": 5}}, "121": {"start": {"line": 122, "column": 0}, "end": {"line": 122, "column": 80}}, "122": {"start": {"line": 123, "column": 0}, "end": {"line": 123, "column": 54}}, "123": {"start": {"line": 124, "column": 0}, "end": {"line": 124, "column": 53}}, "124": {"start": {"line": 125, "column": 0}, "end": {"line": 125, "column": 90}}, "125": {"start": {"line": 126, "column": 0}, "end": {"line": 126, "column": 138}}, "126": {"start": {"line": 127, "column": 0}, "end": {"line": 127, "column": 46}}, "127": {"start": {"line": 128, "column": 0}, "end": {"line": 128, "column": 49}}, "128": {"start": {"line": 129, "column": 0}, "end": {"line": 129, "column": 31}}, "129": {"start": {"line": 130, "column": 0}, "end": {"line": 130, "column": 31}}, "130": {"start": {"line": 131, "column": 0}, "end": {"line": 131, "column": 9}}, "131": {"start": {"line": 132, "column": 0}, "end": {"line": 132, "column": 88}}, "132": {"start": {"line": 133, "column": 0}, "end": {"line": 133, "column": 5}}, "135": {"start": {"line": 136, "column": 0}, "end": {"line": 136, "column": 122}}, "136": {"start": {"line": 137, "column": 0}, "end": {"line": 137, "column": 56}}, "139": {"start": {"line": 140, "column": 0}, "end": {"line": 140, "column": 60}}, "140": {"start": {"line": 141, "column": 0}, "end": {"line": 141, "column": 30}}, "141": {"start": {"line": 142, "column": 0}, "end": {"line": 142, "column": 17}}, "142": {"start": {"line": 143, "column": 0}, "end": {"line": 143, "column": 48}}, "143": {"start": {"line": 144, "column": 0}, "end": {"line": 144, "column": 28}}, "144": {"start": {"line": 145, "column": 0}, "end": {"line": 145, "column": 15}}, "145": {"start": {"line": 146, "column": 0}, "end": {"line": 146, "column": 5}}, "146": {"start": {"line": 147, "column": 0}, "end": {"line": 147, "column": 171}}, "148": {"start": {"line": 149, "column": 0}, "end": {"line": 149, "column": 90}}, "149": {"start": {"line": 150, "column": 0}, "end": {"line": 150, "column": 28}}, "150": {"start": {"line": 151, "column": 0}, "end": {"line": 151, "column": 18}}, "151": {"start": {"line": 152, "column": 0}, "end": {"line": 152, "column": 4}}, "153": {"start": {"line": 154, "column": 0}, "end": {"line": 154, "column": 60}}, "158": {"start": {"line": 159, "column": 0}, "end": {"line": 159, "column": 64}}, "159": {"start": {"line": 160, "column": 0}, "end": {"line": 160, "column": 4}}, "161": {"start": {"line": 162, "column": 0}, "end": {"line": 162, "column": 67}}, "166": {"start": {"line": 167, "column": 0}, "end": {"line": 167, "column": 66}}, "167": {"start": {"line": 168, "column": 0}, "end": {"line": 168, "column": 4}}, "169": {"start": {"line": 170, "column": 0}, "end": {"line": 170, "column": 36}}, "171": {"start": {"line": 172, "column": 0}, "end": {"line": 172, "column": 66}}, "172": {"start": {"line": 173, "column": 0}, "end": {"line": 173, "column": 113}}, "173": {"start": {"line": 174, "column": 0}, "end": {"line": 174, "column": 18}}, "174": {"start": {"line": 175, "column": 0}, "end": {"line": 175, "column": 18}}, "175": {"start": {"line": 176, "column": 0}, "end": {"line": 176, "column": 5}}, "176": {"start": {"line": 177, "column": 0}, "end": {"line": 177, "column": 25}}, "177": {"start": {"line": 178, "column": 0}, "end": {"line": 178, "column": 4}}, "180": {"start": {"line": 181, "column": 0}, "end": {"line": 181, "column": 79}}, "181": {"start": {"line": 182, "column": 0}, "end": {"line": 182, "column": 56}}, "182": {"start": {"line": 183, "column": 0}, "end": {"line": 183, "column": 39}}, "183": {"start": {"line": 184, "column": 0}, "end": {"line": 184, "column": 18}}, "184": {"start": {"line": 185, "column": 0}, "end": {"line": 185, "column": 5}}, "186": {"start": {"line": 187, "column": 0}, "end": {"line": 187, "column": 37}}, "187": {"start": {"line": 188, "column": 0}, "end": {"line": 188, "column": 27}}, "188": {"start": {"line": 189, "column": 0}, "end": {"line": 189, "column": 13}}, "189": {"start": {"line": 190, "column": 0}, "end": {"line": 190, "column": 86}}, "190": {"start": {"line": 191, "column": 0}, "end": {"line": 191, "column": 44}}, "191": {"start": {"line": 192, "column": 0}, "end": {"line": 192, "column": 24}}, "192": {"start": {"line": 193, "column": 0}, "end": {"line": 193, "column": 40}}, "193": {"start": {"line": 194, "column": 0}, "end": {"line": 194, "column": 7}}, "195": {"start": {"line": 196, "column": 0}, "end": {"line": 196, "column": 34}}, "197": {"start": {"line": 198, "column": 0}, "end": {"line": 198, "column": 24}}, "198": {"start": {"line": 199, "column": 0}, "end": {"line": 199, "column": 27}}, "201": {"start": {"line": 202, "column": 0}, "end": {"line": 202, "column": 49}}, "203": {"start": {"line": 204, "column": 0}, "end": {"line": 204, "column": 84}}, "204": {"start": {"line": 205, "column": 0}, "end": {"line": 205, "column": 96}}, "205": {"start": {"line": 206, "column": 0}, "end": {"line": 206, "column": 18}}, "206": {"start": {"line": 207, "column": 0}, "end": {"line": 207, "column": 5}}, "209": {"start": {"line": 210, "column": 0}, "end": {"line": 210, "column": 57}}, "210": {"start": {"line": 211, "column": 0}, "end": {"line": 211, "column": 110}}, "212": {"start": {"line": 213, "column": 0}, "end": {"line": 213, "column": 30}}, "213": {"start": {"line": 214, "column": 0}, "end": {"line": 214, "column": 17}}, "214": {"start": {"line": 215, "column": 0}, "end": {"line": 215, "column": 32}}, "215": {"start": {"line": 216, "column": 0}, "end": {"line": 216, "column": 32}}, "216": {"start": {"line": 217, "column": 0}, "end": {"line": 217, "column": 29}}, "217": {"start": {"line": 218, "column": 0}, "end": {"line": 218, "column": 6}}, "219": {"start": {"line": 220, "column": 0}, "end": {"line": 220, "column": 29}}, "220": {"start": {"line": 221, "column": 0}, "end": {"line": 221, "column": 19}}, "221": {"start": {"line": 222, "column": 0}, "end": {"line": 222, "column": 4}}, "224": {"start": {"line": 225, "column": 0}, "end": {"line": 225, "column": 49}}, "225": {"start": {"line": 226, "column": 0}, "end": {"line": 226, "column": 37}}, "226": {"start": {"line": 227, "column": 0}, "end": {"line": 227, "column": 27}}, "229": {"start": {"line": 230, "column": 0}, "end": {"line": 230, "column": 34}}, "230": {"start": {"line": 231, "column": 0}, "end": {"line": 231, "column": 13}}, "231": {"start": {"line": 232, "column": 0}, "end": {"line": 232, "column": 73}}, "232": {"start": {"line": 233, "column": 0}, "end": {"line": 233, "column": 18}}, "233": {"start": {"line": 234, "column": 0}, "end": {"line": 234, "column": 24}}, "234": {"start": {"line": 235, "column": 0}, "end": {"line": 235, "column": 47}}, "235": {"start": {"line": 236, "column": 0}, "end": {"line": 236, "column": 7}}, "237": {"start": {"line": 238, "column": 0}, "end": {"line": 238, "column": 52}}, "240": {"start": {"line": 241, "column": 0}, "end": {"line": 241, "column": 64}}, "242": {"start": {"line": 243, "column": 0}, "end": {"line": 243, "column": 27}}, "243": {"start": {"line": 244, "column": 0}, "end": {"line": 244, "column": 65}}, "244": {"start": {"line": 245, "column": 0}, "end": {"line": 245, "column": 5}}, "245": {"start": {"line": 246, "column": 0}, "end": {"line": 246, "column": 4}}, "247": {"start": {"line": 248, "column": 0}, "end": {"line": 248, "column": 16}}, "249": {"start": {"line": 250, "column": 0}, "end": {"line": 250, "column": 25}}, "250": {"start": {"line": 251, "column": 0}, "end": {"line": 251, "column": 4}}, "252": {"start": {"line": 253, "column": 0}, "end": {"line": 253, "column": 60}}, "255": {"start": {"line": 256, "column": 0}, "end": {"line": 256, "column": 28}}, "256": {"start": {"line": 257, "column": 0}, "end": {"line": 257, "column": 3}}, "257": {"start": {"line": 258, "column": 0}, "end": {"line": 258, "column": 1}}}, "s": {"0": 1, "1": 1, "2": 1, "3": 1, "4": 1, "9": 1, "10": 1, "13": 1, "14": 1, "15": 1, "16": 1, "17": 1, "18": 1, "20": 0, "22": 0, "23": 0, "24": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "50": 1, "51": 1, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "62": 1, "64": 4, "65": 4, "66": 1, "67": 1, "69": 3, "70": 3, "71": 3, "72": 3, "74": 0, "75": 0, "76": 0, "77": 0, "78": 0, "79": 0, "80": 4, "82": 1, "84": 1, "85": 1, "86": 1, "87": 1, "88": 1, "90": 0, "91": 0, "92": 0, "93": 0, "94": 0, "97": 0, "98": 0, "99": 0, "100": 0, "102": 0, "103": 0, "104": 0, "105": 0, "106": 0, "107": 0, "108": 0, "109": 0, "110": 0, "111": 0, "112": 1, "113": 1, "115": 1, "116": 1, "117": 0, "118": 0, "119": 0, "121": 0, "122": 0, "123": 0, "124": 0, "125": 0, "126": 0, "127": 0, "128": 0, "129": 0, "130": 0, "131": 0, "132": 0, "135": 0, "136": 0, "139": 0, "140": 0, "141": 0, "142": 0, "143": 0, "144": 0, "145": 0, "146": 0, "148": 0, "149": 0, "150": 0, "151": 1, "153": 1, "158": 0, "159": 0, "161": 1, "166": 0, "167": 0, "169": 1, "171": 0, "172": 0, "173": 0, "174": 0, "175": 0, "176": 0, "177": 0, "180": 1, "181": 0, "182": 0, "183": 0, "184": 0, "186": 0, "187": 0, "188": 0, "189": 0, "190": 0, "191": 0, "192": 0, "193": 0, "195": 0, "197": 0, "198": 0, "201": 0, "203": 0, "204": 0, "205": 0, "206": 0, "209": 0, "210": 0, "212": 0, "213": 0, "214": 0, "215": 0, "216": 0, "217": 0, "219": 0, "220": 0, "221": 0, "224": 1, "225": 0, "226": 0, "229": 0, "230": 0, "231": 0, "232": 0, "233": 0, "234": 0, "235": 0, "237": 0, "240": 0, "242": 0, "243": 0, "244": 0, "245": 0, "247": 1, "249": 3, "250": 3, "252": 1, "255": 0, "256": 0, "257": 1}, "branchMap": {"0": {"type": "branch", "line": 63, "loc": {"start": {"line": 63, "column": 2}, "end": {"line": 81, "column": 4}}, "locations": [{"start": {"line": 63, "column": 2}, "end": {"line": 81, "column": 4}}]}, "1": {"type": "branch", "line": 66, "loc": {"start": {"line": 66, "column": 38}, "end": {"line": 68, "column": 5}}, "locations": [{"start": {"line": 66, "column": 38}, "end": {"line": 68, "column": 5}}]}, "2": {"type": "branch", "line": 68, "loc": {"start": {"line": 68, "column": 4}, "end": {"line": 73, "column": 5}}, "locations": [{"start": {"line": 68, "column": 4}, "end": {"line": 73, "column": 5}}]}, "3": {"type": "branch", "line": 73, "loc": {"start": {"line": 73, "column": 4}, "end": {"line": 80, "column": 158}}, "locations": [{"start": {"line": 73, "column": 4}, "end": {"line": 80, "column": 158}}]}, "4": {"type": "branch", "line": 83, "loc": {"start": {"line": 83, "column": 2}, "end": {"line": 152, "column": 4}}, "locations": [{"start": {"line": 83, "column": 2}, "end": {"line": 152, "column": 4}}]}, "5": {"type": "branch", "line": 89, "loc": {"start": {"line": 89, "column": 4}, "end": {"line": 113, "column": 76}}, "locations": [{"start": {"line": 89, "column": 4}, "end": {"line": 113, "column": 76}}]}, "6": {"type": "branch", "line": 113, "loc": {"start": {"line": 113, "column": 69}, "end": {"line": 113, "column": 100}}, "locations": [{"start": {"line": 113, "column": 69}, "end": {"line": 113, "column": 100}}]}, "7": {"type": "branch", "line": 114, "loc": {"start": {"line": 114, "column": 55}, "end": {"line": 114, "column": 79}}, "locations": [{"start": {"line": 114, "column": 55}, "end": {"line": 114, "column": 79}}]}, "8": {"type": "branch", "line": 114, "loc": {"start": {"line": 114, "column": 72}, "end": {"line": 114, "column": 103}}, "locations": [{"start": {"line": 114, "column": 72}, "end": {"line": 114, "column": 103}}]}, "9": {"type": "branch", "line": 117, "loc": {"start": {"line": 117, "column": 39}, "end": {"line": 117, "column": 78}}, "locations": [{"start": {"line": 117, "column": 39}, "end": {"line": 117, "column": 78}}]}, "10": {"type": "branch", "line": 117, "loc": {"start": {"line": 117, "column": 78}, "end": {"line": 151, "column": 18}}, "locations": [{"start": {"line": 117, "column": 78}, "end": {"line": 151, "column": 18}}]}, "11": {"type": "branch", "line": 248, "loc": {"start": {"line": 248, "column": 2}, "end": {"line": 251, "column": 4}}, "locations": [{"start": {"line": 248, "column": 2}, "end": {"line": 251, "column": 4}}]}}, "b": {"0": [4], "1": [1], "2": [3], "3": [0], "4": [1], "5": [0], "6": [0], "7": [0], "8": [0], "9": [0], "10": [0], "11": [3]}, "fnMap": {"0": {"name": "hkdf", "decl": {"start": {"line": 21, "column": 0}, "end": {"line": 25, "column": 1}}, "loc": {"start": {"line": 21, "column": 0}, "end": {"line": 25, "column": 1}}, "line": 21}, "1": {"name": "derivePinHash", "decl": {"start": {"line": 27, "column": 0}, "end": {"line": 42, "column": 1}}, "loc": {"start": {"line": 27, "column": 0}, "end": {"line": 42, "column": 1}}, "line": 27}, "2": {"name": "timingSafeEqual", "decl": {"start": {"line": 44, "column": 0}, "end": {"line": 49, "column": 1}}, "loc": {"start": {"line": 44, "column": 0}, "end": {"line": 49, "column": 1}}, "line": 44}, "3": {"name": "ensureLocalAccount", "decl": {"start": {"line": 52, "column": 2}, "end": {"line": 61, "column": 4}}, "loc": {"start": {"line": 52, "column": 2}, "end": {"line": 61, "column": 4}}, "line": 52}, "4": {"name": "setPin", "decl": {"start": {"line": 63, "column": 2}, "end": {"line": 81, "column": 4}}, "loc": {"start": {"line": 63, "column": 2}, "end": {"line": 81, "column": 4}}, "line": 63}, "5": {"name": "verifyPin", "decl": {"start": {"line": 83, "column": 2}, "end": {"line": 152, "column": 4}}, "loc": {"start": {"line": 83, "column": 2}, "end": {"line": 152, "column": 4}}, "line": 83}, "6": {"name": "callback", "decl": {"start": {"line": 109, "column": 16}, "end": {"line": 109, "column": 39}}, "loc": {"start": {"line": 109, "column": 16}, "end": {"line": 109, "column": 39}}, "line": 109}, "7": {"name": "registerWebAuthn", "decl": {"start": {"line": 154, "column": 2}, "end": {"line": 160, "column": 4}}, "loc": {"start": {"line": 154, "column": 2}, "end": {"line": 160, "column": 4}}, "line": 154}, "8": {"name": "authenticateWebAuthn", "decl": {"start": {"line": 162, "column": 2}, "end": {"line": 168, "column": 4}}, "loc": {"start": {"line": 162, "column": 2}, "end": {"line": 168, "column": 4}}, "line": 162}, "9": {"name": "currentSession", "decl": {"start": {"line": 170, "column": 2}, "end": {"line": 178, "column": 4}}, "loc": {"start": {"line": 170, "column": 2}, "end": {"line": 178, "column": 4}}, "line": 170}, "10": {"name": "validateAndRefreshSession", "decl": {"start": {"line": 181, "column": 2}, "end": {"line": 222, "column": 4}}, "loc": {"start": {"line": 181, "column": 2}, "end": {"line": 222, "column": 4}}, "line": 181}, "11": {"name": "cleanupExpiredSessions", "decl": {"start": {"line": 225, "column": 2}, "end": {"line": 246, "column": 4}}, "loc": {"start": {"line": 225, "column": 2}, "end": {"line": 246, "column": 4}}, "line": 225}, "12": {"name": "lock", "decl": {"start": {"line": 248, "column": 2}, "end": {"line": 251, "column": 4}}, "loc": {"start": {"line": 248, "column": 2}, "end": {"line": 251, "column": 4}}, "line": 248}, "13": {"name": "unlockWithSession", "decl": {"start": {"line": 253, "column": 2}, "end": {"line": 257, "column": 3}}, "loc": {"start": {"line": 253, "column": 2}, "end": {"line": 257, "column": 3}}, "line": 253}}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 4, "5": 1, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 3, "13": 0}}, "/Users/<USER>/Documents/SizeWise_Offline_App/core/auth/idleLock.ts": {"path": "/Users/<USER>/Documents/SizeWise_Offline_App/core/auth/idleLock.ts", "all": true, "statementMap": {"1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 37}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 15}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 45}}, "6": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 18}}, "7": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 22}}, "8": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": 28}}, "9": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": 16}}, "10": {"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 65}}, "11": {"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 17}}, "12": {"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 1}}, "14": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 31}}, "15": {"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 96}}, "16": {"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 58}}, "17": {"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 5}}, "18": {"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 10}}, "19": {"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 1}}}, "s": {"1": 0, "3": 0, "4": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 497}, "end": {"line": 20, "column": 1}}, "locations": [{"start": {"line": 1, "column": 497}, "end": {"line": 20, "column": 1}}]}}, "b": {"0": [1]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 497}, "end": {"line": 20, "column": 1}}, "loc": {"start": {"line": 1, "column": 497}, "end": {"line": 20, "column": 1}}, "line": 1}}, "f": {"0": 1}}, "/Users/<USER>/Documents/SizeWise_Offline_App/db/dao.ts": {"path": "/Users/<USER>/Documents/SizeWise_Offline_App/db/dao.ts", "all": false, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 34}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 39}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 34}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 47}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 43}}, "5": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 8}}, "16": {"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 47}}, "17": {"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 99}}, "18": {"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 19}}, "19": {"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 37}}, "20": {"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 3}}, "21": {"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 1}}, "23": {"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 50}}, "25": {"start": {"line": 26, "column": 0}, "end": {"line": 26, "column": 94}}, "26": {"start": {"line": 27, "column": 0}, "end": {"line": 27, "column": 28}}, "27": {"start": {"line": 28, "column": 0}, "end": {"line": 28, "column": 25}}, "28": {"start": {"line": 29, "column": 0}, "end": {"line": 29, "column": 150}}, "29": {"start": {"line": 30, "column": 0}, "end": {"line": 30, "column": 21}}, "30": {"start": {"line": 31, "column": 0}, "end": {"line": 31, "column": 1}}, "32": {"start": {"line": 33, "column": 0}, "end": {"line": 33, "column": 133}}, "34": {"start": {"line": 35, "column": 0}, "end": {"line": 35, "column": 51}}, "35": {"start": {"line": 36, "column": 0}, "end": {"line": 36, "column": 32}}, "36": {"start": {"line": 37, "column": 0}, "end": {"line": 37, "column": 42}}, "37": {"start": {"line": 38, "column": 0}, "end": {"line": 38, "column": 3}}, "39": {"start": {"line": 40, "column": 0}, "end": {"line": 40, "column": 65}}, "40": {"start": {"line": 41, "column": 0}, "end": {"line": 41, "column": 32}}, "41": {"start": {"line": 42, "column": 0}, "end": {"line": 42, "column": 42}}, "42": {"start": {"line": 43, "column": 0}, "end": {"line": 43, "column": 3}}, "44": {"start": {"line": 45, "column": 0}, "end": {"line": 45, "column": 56}}, "45": {"start": {"line": 46, "column": 0}, "end": {"line": 46, "column": 36}}, "46": {"start": {"line": 47, "column": 0}, "end": {"line": 47, "column": 46}}, "47": {"start": {"line": 48, "column": 0}, "end": {"line": 48, "column": 3}}, "49": {"start": {"line": 50, "column": 0}, "end": {"line": 50, "column": 50}}, "50": {"start": {"line": 51, "column": 0}, "end": {"line": 51, "column": 32}}, "51": {"start": {"line": 52, "column": 0}, "end": {"line": 52, "column": 42}}, "52": {"start": {"line": 53, "column": 0}, "end": {"line": 53, "column": 3}}, "55": {"start": {"line": 56, "column": 0}, "end": {"line": 56, "column": 50}}, "56": {"start": {"line": 57, "column": 0}, "end": {"line": 57, "column": 57}}, "57": {"start": {"line": 58, "column": 0}, "end": {"line": 58, "column": 58}}, "58": {"start": {"line": 59, "column": 0}, "end": {"line": 59, "column": 69}}, "61": {"start": {"line": 62, "column": 0}, "end": {"line": 62, "column": 52}}, "62": {"start": {"line": 63, "column": 0}, "end": {"line": 63, "column": 55}}, "63": {"start": {"line": 64, "column": 0}, "end": {"line": 64, "column": 33}}, "64": {"start": {"line": 65, "column": 0}, "end": {"line": 65, "column": 30}}, "65": {"start": {"line": 66, "column": 0}, "end": {"line": 66, "column": 113}}, "66": {"start": {"line": 67, "column": 0}, "end": {"line": 67, "column": 30}}, "67": {"start": {"line": 68, "column": 0}, "end": {"line": 68, "column": 25}}, "68": {"start": {"line": 69, "column": 0}, "end": {"line": 69, "column": 6}}, "69": {"start": {"line": 70, "column": 0}, "end": {"line": 70, "column": 3}}, "71": {"start": {"line": 72, "column": 0}, "end": {"line": 72, "column": 28}}, "72": {"start": {"line": 73, "column": 0}, "end": {"line": 73, "column": 25}}, "73": {"start": {"line": 74, "column": 0}, "end": {"line": 74, "column": 20}}, "76": {"start": {"line": 77, "column": 0}, "end": {"line": 77, "column": 185}}, "77": {"start": {"line": 78, "column": 0}, "end": {"line": 78, "column": 264}}, "80": {"start": {"line": 81, "column": 0}, "end": {"line": 81, "column": 7}}, "81": {"start": {"line": 82, "column": 0}, "end": {"line": 82, "column": 25}}, "82": {"start": {"line": 83, "column": 0}, "end": {"line": 83, "column": 9}}, "83": {"start": {"line": 84, "column": 0}, "end": {"line": 84, "column": 26}}, "84": {"start": {"line": 85, "column": 0}, "end": {"line": 85, "column": 34}}, "85": {"start": {"line": 86, "column": 0}, "end": {"line": 86, "column": 40}}, "86": {"start": {"line": 87, "column": 0}, "end": {"line": 87, "column": 33}}, "87": {"start": {"line": 88, "column": 0}, "end": {"line": 88, "column": 39}}, "88": {"start": {"line": 89, "column": 0}, "end": {"line": 89, "column": 22}}, "89": {"start": {"line": 90, "column": 0}, "end": {"line": 90, "column": 20}}, "90": {"start": {"line": 91, "column": 0}, "end": {"line": 91, "column": 18}}, "91": {"start": {"line": 92, "column": 0}, "end": {"line": 92, "column": 6}}, "92": {"start": {"line": 93, "column": 0}, "end": {"line": 93, "column": 64}}, "93": {"start": {"line": 94, "column": 0}, "end": {"line": 94, "column": 19}}, "94": {"start": {"line": 95, "column": 0}, "end": {"line": 95, "column": 62}}, "96": {"start": {"line": 97, "column": 0}, "end": {"line": 97, "column": 3}}, "98": {"start": {"line": 99, "column": 0}, "end": {"line": 99, "column": 12}}, "99": {"start": {"line": 100, "column": 0}, "end": {"line": 100, "column": 1}}, "102": {"start": {"line": 103, "column": 0}, "end": {"line": 103, "column": 123}}, "103": {"start": {"line": 104, "column": 0}, "end": {"line": 104, "column": 28}}, "104": {"start": {"line": 105, "column": 0}, "end": {"line": 105, "column": 25}}, "105": {"start": {"line": 106, "column": 0}, "end": {"line": 106, "column": 164}}, "106": {"start": {"line": 107, "column": 0}, "end": {"line": 107, "column": 21}}, "107": {"start": {"line": 108, "column": 0}, "end": {"line": 108, "column": 1}}, "109": {"start": {"line": 110, "column": 0}, "end": {"line": 110, "column": 123}}, "111": {"start": {"line": 112, "column": 0}, "end": {"line": 112, "column": 54}}, "112": {"start": {"line": 113, "column": 0}, "end": {"line": 113, "column": 37}}, "113": {"start": {"line": 114, "column": 0}, "end": {"line": 114, "column": 72}}, "114": {"start": {"line": 115, "column": 0}, "end": {"line": 115, "column": 3}}, "116": {"start": {"line": 117, "column": 0}, "end": {"line": 117, "column": 41}}, "117": {"start": {"line": 118, "column": 0}, "end": {"line": 118, "column": 29}}, "118": {"start": {"line": 119, "column": 0}, "end": {"line": 119, "column": 66}}, "119": {"start": {"line": 120, "column": 0}, "end": {"line": 120, "column": 3}}, "121": {"start": {"line": 122, "column": 0}, "end": {"line": 122, "column": 41}}, "122": {"start": {"line": 123, "column": 0}, "end": {"line": 123, "column": 29}}, "123": {"start": {"line": 124, "column": 0}, "end": {"line": 124, "column": 66}}, "124": {"start": {"line": 125, "column": 0}, "end": {"line": 125, "column": 3}}, "126": {"start": {"line": 127, "column": 0}, "end": {"line": 127, "column": 41}}, "127": {"start": {"line": 128, "column": 0}, "end": {"line": 128, "column": 29}}, "128": {"start": {"line": 129, "column": 0}, "end": {"line": 129, "column": 66}}, "129": {"start": {"line": 130, "column": 0}, "end": {"line": 130, "column": 3}}, "131": {"start": {"line": 132, "column": 0}, "end": {"line": 132, "column": 48}}, "132": {"start": {"line": 133, "column": 0}, "end": {"line": 133, "column": 32}}, "133": {"start": {"line": 134, "column": 0}, "end": {"line": 134, "column": 66}}, "134": {"start": {"line": 135, "column": 0}, "end": {"line": 135, "column": 3}}, "136": {"start": {"line": 137, "column": 0}, "end": {"line": 137, "column": 28}}, "137": {"start": {"line": 138, "column": 0}, "end": {"line": 138, "column": 20}}, "138": {"start": {"line": 139, "column": 0}, "end": {"line": 139, "column": 180}}, "139": {"start": {"line": 140, "column": 0}, "end": {"line": 140, "column": 12}}, "140": {"start": {"line": 141, "column": 0}, "end": {"line": 141, "column": 1}}, "143": {"start": {"line": 144, "column": 0}, "end": {"line": 144, "column": 165}}, "144": {"start": {"line": 145, "column": 0}, "end": {"line": 145, "column": 28}}, "145": {"start": {"line": 146, "column": 0}, "end": {"line": 146, "column": 25}}, "146": {"start": {"line": 147, "column": 0}, "end": {"line": 147, "column": 192}}, "147": {"start": {"line": 148, "column": 0}, "end": {"line": 148, "column": 21}}, "148": {"start": {"line": 149, "column": 0}, "end": {"line": 149, "column": 1}}, "150": {"start": {"line": 151, "column": 0}, "end": {"line": 151, "column": 212}}, "152": {"start": {"line": 153, "column": 0}, "end": {"line": 153, "column": 54}}, "153": {"start": {"line": 154, "column": 0}, "end": {"line": 154, "column": 37}}, "154": {"start": {"line": 155, "column": 0}, "end": {"line": 155, "column": 72}}, "155": {"start": {"line": 156, "column": 0}, "end": {"line": 156, "column": 3}}, "157": {"start": {"line": 158, "column": 0}, "end": {"line": 158, "column": 60}}, "158": {"start": {"line": 159, "column": 0}, "end": {"line": 159, "column": 40}}, "159": {"start": {"line": 160, "column": 0}, "end": {"line": 160, "column": 81}}, "160": {"start": {"line": 161, "column": 0}, "end": {"line": 161, "column": 3}}, "162": {"start": {"line": 163, "column": 0}, "end": {"line": 163, "column": 56}}, "163": {"start": {"line": 164, "column": 0}, "end": {"line": 164, "column": 38}}, "164": {"start": {"line": 165, "column": 0}, "end": {"line": 165, "column": 77}}, "165": {"start": {"line": 166, "column": 0}, "end": {"line": 166, "column": 3}}, "168": {"start": {"line": 169, "column": 0}, "end": {"line": 169, "column": 44}}, "169": {"start": {"line": 170, "column": 0}, "end": {"line": 170, "column": 29}}, "170": {"start": {"line": 171, "column": 0}, "end": {"line": 171, "column": 62}}, "171": {"start": {"line": 172, "column": 0}, "end": {"line": 172, "column": 3}}, "173": {"start": {"line": 174, "column": 0}, "end": {"line": 174, "column": 46}}, "174": {"start": {"line": 175, "column": 0}, "end": {"line": 175, "column": 30}}, "175": {"start": {"line": 176, "column": 0}, "end": {"line": 176, "column": 78}}, "176": {"start": {"line": 177, "column": 0}, "end": {"line": 177, "column": 3}}, "178": {"start": {"line": 179, "column": 0}, "end": {"line": 179, "column": 44}}, "179": {"start": {"line": 180, "column": 0}, "end": {"line": 180, "column": 29}}, "180": {"start": {"line": 181, "column": 0}, "end": {"line": 181, "column": 64}}, "181": {"start": {"line": 182, "column": 0}, "end": {"line": 182, "column": 3}}, "183": {"start": {"line": 184, "column": 0}, "end": {"line": 184, "column": 44}}, "184": {"start": {"line": 185, "column": 0}, "end": {"line": 185, "column": 29}}, "185": {"start": {"line": 186, "column": 0}, "end": {"line": 186, "column": 67}}, "186": {"start": {"line": 187, "column": 0}, "end": {"line": 187, "column": 3}}, "188": {"start": {"line": 189, "column": 0}, "end": {"line": 189, "column": 44}}, "189": {"start": {"line": 190, "column": 0}, "end": {"line": 190, "column": 29}}, "190": {"start": {"line": 191, "column": 0}, "end": {"line": 191, "column": 74}}, "191": {"start": {"line": 192, "column": 0}, "end": {"line": 192, "column": 3}}, "193": {"start": {"line": 194, "column": 0}, "end": {"line": 194, "column": 48}}, "194": {"start": {"line": 195, "column": 0}, "end": {"line": 195, "column": 32}}, "195": {"start": {"line": 196, "column": 0}, "end": {"line": 196, "column": 70}}, "196": {"start": {"line": 197, "column": 0}, "end": {"line": 197, "column": 3}}, "198": {"start": {"line": 199, "column": 0}, "end": {"line": 199, "column": 48}}, "199": {"start": {"line": 200, "column": 0}, "end": {"line": 200, "column": 32}}, "200": {"start": {"line": 201, "column": 0}, "end": {"line": 201, "column": 66}}, "201": {"start": {"line": 202, "column": 0}, "end": {"line": 202, "column": 3}}, "204": {"start": {"line": 205, "column": 0}, "end": {"line": 205, "column": 61}}, "205": {"start": {"line": 206, "column": 0}, "end": {"line": 206, "column": 62}}, "206": {"start": {"line": 207, "column": 0}, "end": {"line": 207, "column": 33}}, "207": {"start": {"line": 208, "column": 0}, "end": {"line": 208, "column": 30}}, "208": {"start": {"line": 209, "column": 0}, "end": {"line": 209, "column": 140}}, "209": {"start": {"line": 210, "column": 0}, "end": {"line": 210, "column": 37}}, "210": {"start": {"line": 211, "column": 0}, "end": {"line": 211, "column": 25}}, "211": {"start": {"line": 212, "column": 0}, "end": {"line": 212, "column": 6}}, "212": {"start": {"line": 213, "column": 0}, "end": {"line": 213, "column": 3}}, "214": {"start": {"line": 215, "column": 0}, "end": {"line": 215, "column": 28}}, "215": {"start": {"line": 216, "column": 0}, "end": {"line": 216, "column": 20}}, "216": {"start": {"line": 217, "column": 0}, "end": {"line": 217, "column": 329}}, "217": {"start": {"line": 218, "column": 0}, "end": {"line": 218, "column": 12}}, "218": {"start": {"line": 219, "column": 0}, "end": {"line": 219, "column": 1}}, "220": {"start": {"line": 221, "column": 0}, "end": {"line": 221, "column": 73}}, "221": {"start": {"line": 222, "column": 0}, "end": {"line": 222, "column": 28}}, "222": {"start": {"line": 223, "column": 0}, "end": {"line": 223, "column": 105}}, "223": {"start": {"line": 224, "column": 0}, "end": {"line": 224, "column": 1}}, "225": {"start": {"line": 226, "column": 0}, "end": {"line": 226, "column": 56}}, "226": {"start": {"line": 227, "column": 0}, "end": {"line": 227, "column": 28}}, "227": {"start": {"line": 228, "column": 0}, "end": {"line": 228, "column": 71}}, "228": {"start": {"line": 229, "column": 0}, "end": {"line": 229, "column": 1}}, "231": {"start": {"line": 232, "column": 0}, "end": {"line": 232, "column": 120}}, "232": {"start": {"line": 233, "column": 0}, "end": {"line": 233, "column": 40}}, "233": {"start": {"line": 234, "column": 0}, "end": {"line": 234, "column": 52}}, "234": {"start": {"line": 235, "column": 0}, "end": {"line": 235, "column": 10}}, "235": {"start": {"line": 236, "column": 0}, "end": {"line": 236, "column": 12}}, "236": {"start": {"line": 237, "column": 0}, "end": {"line": 237, "column": 101}}, "237": {"start": {"line": 238, "column": 0}, "end": {"line": 238, "column": 12}}, "238": {"start": {"line": 239, "column": 0}, "end": {"line": 239, "column": 34}}, "239": {"start": {"line": 240, "column": 0}, "end": {"line": 240, "column": 4}}, "240": {"start": {"line": 241, "column": 0}, "end": {"line": 241, "column": 1}}, "242": {"start": {"line": 243, "column": 0}, "end": {"line": 243, "column": 137}}, "243": {"start": {"line": 244, "column": 0}, "end": {"line": 244, "column": 49}}, "244": {"start": {"line": 245, "column": 0}, "end": {"line": 245, "column": 59}}, "245": {"start": {"line": 246, "column": 0}, "end": {"line": 246, "column": 10}}, "246": {"start": {"line": 247, "column": 0}, "end": {"line": 247, "column": 12}}, "247": {"start": {"line": 248, "column": 0}, "end": {"line": 248, "column": 120}}, "248": {"start": {"line": 249, "column": 0}, "end": {"line": 249, "column": 12}}, "249": {"start": {"line": 250, "column": 0}, "end": {"line": 250, "column": 41}}, "250": {"start": {"line": 251, "column": 0}, "end": {"line": 251, "column": 4}}, "251": {"start": {"line": 252, "column": 0}, "end": {"line": 252, "column": 1}}, "254": {"start": {"line": 255, "column": 0}, "end": {"line": 255, "column": 71}}, "255": {"start": {"line": 256, "column": 0}, "end": {"line": 256, "column": 28}}, "258": {"start": {"line": 259, "column": 0}, "end": {"line": 259, "column": 103}}, "259": {"start": {"line": 260, "column": 0}, "end": {"line": 260, "column": 116}}, "262": {"start": {"line": 263, "column": 0}, "end": {"line": 263, "column": 107}}, "265": {"start": {"line": 266, "column": 0}, "end": {"line": 266, "column": 105}}, "267": {"start": {"line": 268, "column": 0}, "end": {"line": 268, "column": 10}}, "268": {"start": {"line": 269, "column": 0}, "end": {"line": 269, "column": 19}}, "269": {"start": {"line": 270, "column": 0}, "end": {"line": 270, "column": 19}}, "270": {"start": {"line": 271, "column": 0}, "end": {"line": 271, "column": 14}}, "271": {"start": {"line": 272, "column": 0}, "end": {"line": 272, "column": 13}}, "272": {"start": {"line": 273, "column": 0}, "end": {"line": 273, "column": 35}}, "273": {"start": {"line": 274, "column": 0}, "end": {"line": 274, "column": 4}}, "274": {"start": {"line": 275, "column": 0}, "end": {"line": 275, "column": 1}}, "277": {"start": {"line": 278, "column": 0}, "end": {"line": 278, "column": 127}}, "278": {"start": {"line": 279, "column": 0}, "end": {"line": 279, "column": 7}}, "279": {"start": {"line": 280, "column": 0}, "end": {"line": 280, "column": 56}}, "280": {"start": {"line": 281, "column": 0}, "end": {"line": 281, "column": 86}}, "283": {"start": {"line": 284, "column": 0}, "end": {"line": 284, "column": 53}}, "285": {"start": {"line": 286, "column": 0}, "end": {"line": 286, "column": 22}}, "286": {"start": {"line": 287, "column": 0}, "end": {"line": 287, "column": 19}}, "287": {"start": {"line": 288, "column": 0}, "end": {"line": 288, "column": 62}}, "288": {"start": {"line": 289, "column": 0}, "end": {"line": 289, "column": 14}}, "289": {"start": {"line": 290, "column": 0}, "end": {"line": 290, "column": 3}}, "290": {"start": {"line": 291, "column": 0}, "end": {"line": 291, "column": 1}}}, "s": {"0": 1, "1": 1, "2": 1, "3": 1, "4": 1, "5": 1, "16": 1, "17": 1, "18": 5, "19": 5, "20": 5, "21": 1, "23": 1, "25": 1, "26": 1, "27": 1, "28": 1, "29": 1, "30": 1, "32": 13, "34": 13, "35": 13, "36": 4, "37": 4, "39": 9, "40": 9, "41": 4, "42": 4, "44": 5, "45": 5, "46": 0, "47": 0, "49": 5, "50": 5, "51": 0, "52": 0, "55": 5, "56": 5, "57": 5, "58": 5, "61": 5, "62": 5, "63": 2, "64": 2, "65": 2, "66": 2, "67": 2, "68": 2, "69": 2, "71": 3, "72": 3, "73": 3, "76": 3, "77": 3, "80": 3, "81": 3, "82": 3, "83": 3, "84": 3, "85": 3, "86": 3, "87": 3, "88": 3, "89": 3, "90": 3, "91": 3, "92": 3, "93": 5, "94": 0, "96": 0, "98": 2, "99": 2, "102": 1, "103": 1, "104": 1, "105": 1, "106": 1, "107": 1, "109": 4, "111": 4, "112": 4, "113": 1, "114": 1, "116": 3, "117": 4, "118": 1, "119": 1, "121": 2, "122": 2, "123": 1, "124": 1, "126": 1, "127": 1, "128": 0, "129": 0, "131": 1, "132": 1, "133": 0, "134": 0, "136": 1, "137": 1, "138": 1, "139": 1, "140": 1, "143": 1, "144": 1, "145": 1, "146": 1, "147": 1, "148": 1, "150": 5, "152": 5, "153": 5, "154": 1, "155": 1, "157": 4, "158": 4, "159": 0, "160": 0, "162": 4, "163": 4, "164": 0, "165": 0, "168": 4, "169": 4, "170": 0, "171": 0, "173": 4, "174": 4, "175": 0, "176": 0, "178": 4, "179": 4, "180": 0, "181": 0, "183": 4, "184": 4, "185": 0, "186": 0, "188": 4, "189": 4, "190": 0, "191": 0, "193": 4, "194": 4, "195": 0, "196": 0, "198": 4, "199": 4, "200": 0, "201": 0, "204": 4, "205": 4, "206": 2, "207": 2, "208": 2, "209": 2, "210": 2, "211": 2, "212": 2, "214": 2, "215": 2, "216": 2, "217": 2, "218": 2, "220": 7, "221": 7, "222": 7, "223": 7, "225": 9, "226": 9, "227": 9, "228": 9, "231": 2, "232": 2, "233": 2, "234": 2, "235": 2, "236": 2, "237": 2, "238": 2, "239": 2, "240": 2, "242": 2, "243": 2, "244": 2, "245": 2, "246": 2, "247": 2, "248": 2, "249": 2, "250": 2, "251": 2, "254": 0, "255": 0, "258": 0, "259": 0, "262": 0, "265": 0, "267": 0, "268": 0, "269": 0, "270": 0, "271": 0, "272": 0, "273": 0, "274": 0, "277": 0, "278": 0, "279": 0, "280": 0, "283": 0, "285": 0, "286": 0, "287": 0, "288": 0, "289": 0, "290": 0}, "branchMap": {"0": {"type": "branch", "line": 18, "loc": {"start": {"line": 18, "column": 2}, "end": {"line": 21, "column": 3}}, "locations": [{"start": {"line": 18, "column": 2}, "end": {"line": 21, "column": 3}}]}, "1": {"type": "branch", "line": 24, "loc": {"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 50}}, "locations": [{"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 50}}]}, "2": {"type": "branch", "line": 26, "loc": {"start": {"line": 26, "column": 0}, "end": {"line": 31, "column": 1}}, "locations": [{"start": {"line": 26, "column": 0}, "end": {"line": 31, "column": 1}}]}, "3": {"type": "branch", "line": 29, "loc": {"start": {"line": 29, "column": 125}, "end": {"line": 29, "column": 147}}, "locations": [{"start": {"line": 29, "column": 125}, "end": {"line": 29, "column": 147}}]}, "4": {"type": "branch", "line": 33, "loc": {"start": {"line": 33, "column": 0}, "end": {"line": 100, "column": 1}}, "locations": [{"start": {"line": 33, "column": 0}, "end": {"line": 100, "column": 1}}]}, "5": {"type": "branch", "line": 36, "loc": {"start": {"line": 36, "column": 31}, "end": {"line": 43, "column": 3}}, "locations": [{"start": {"line": 36, "column": 31}, "end": {"line": 43, "column": 3}}]}, "6": {"type": "branch", "line": 36, "loc": {"start": {"line": 36, "column": 31}, "end": {"line": 38, "column": 3}}, "locations": [{"start": {"line": 36, "column": 31}, "end": {"line": 38, "column": 3}}]}, "7": {"type": "branch", "line": 41, "loc": {"start": {"line": 41, "column": 31}, "end": {"line": 43, "column": 3}}, "locations": [{"start": {"line": 41, "column": 31}, "end": {"line": 43, "column": 3}}]}, "8": {"type": "branch", "line": 43, "loc": {"start": {"line": 43, "column": 2}, "end": {"line": 100, "column": 1}}, "locations": [{"start": {"line": 43, "column": 2}, "end": {"line": 100, "column": 1}}]}, "9": {"type": "branch", "line": 46, "loc": {"start": {"line": 46, "column": 35}, "end": {"line": 48, "column": 3}}, "locations": [{"start": {"line": 46, "column": 35}, "end": {"line": 48, "column": 3}}]}, "10": {"type": "branch", "line": 51, "loc": {"start": {"line": 51, "column": 31}, "end": {"line": 53, "column": 3}}, "locations": [{"start": {"line": 51, "column": 31}, "end": {"line": 53, "column": 3}}]}, "11": {"type": "branch", "line": 63, "loc": {"start": {"line": 63, "column": 54}, "end": {"line": 70, "column": 3}}, "locations": [{"start": {"line": 63, "column": 54}, "end": {"line": 70, "column": 3}}]}, "12": {"type": "branch", "line": 70, "loc": {"start": {"line": 70, "column": 2}, "end": {"line": 93, "column": 64}}, "locations": [{"start": {"line": 70, "column": 2}, "end": {"line": 93, "column": 64}}]}, "13": {"type": "branch", "line": 93, "loc": {"start": {"line": 93, "column": 62}, "end": {"line": 94, "column": 11}}, "locations": [{"start": {"line": 93, "column": 62}, "end": {"line": 94, "column": 11}}]}, "14": {"type": "branch", "line": 94, "loc": {"start": {"line": 94, "column": 2}, "end": {"line": 97, "column": 3}}, "locations": [{"start": {"line": 94, "column": 2}, "end": {"line": 97, "column": 3}}]}, "15": {"type": "branch", "line": 97, "loc": {"start": {"line": 97, "column": 2}, "end": {"line": 100, "column": 1}}, "locations": [{"start": {"line": 97, "column": 2}, "end": {"line": 100, "column": 1}}]}, "16": {"type": "branch", "line": 103, "loc": {"start": {"line": 103, "column": 0}, "end": {"line": 108, "column": 1}}, "locations": [{"start": {"line": 103, "column": 0}, "end": {"line": 108, "column": 1}}]}, "17": {"type": "branch", "line": 106, "loc": {"start": {"line": 106, "column": 139}, "end": {"line": 106, "column": 161}}, "locations": [{"start": {"line": 106, "column": 139}, "end": {"line": 106, "column": 161}}]}, "18": {"type": "branch", "line": 110, "loc": {"start": {"line": 110, "column": 0}, "end": {"line": 141, "column": 1}}, "locations": [{"start": {"line": 110, "column": 0}, "end": {"line": 141, "column": 1}}]}, "19": {"type": "branch", "line": 113, "loc": {"start": {"line": 113, "column": 36}, "end": {"line": 115, "column": 3}}, "locations": [{"start": {"line": 113, "column": 36}, "end": {"line": 115, "column": 3}}]}, "20": {"type": "branch", "line": 115, "loc": {"start": {"line": 115, "column": 2}, "end": {"line": 118, "column": 28}}, "locations": [{"start": {"line": 115, "column": 2}, "end": {"line": 118, "column": 28}}]}, "21": {"type": "branch", "line": 118, "loc": {"start": {"line": 118, "column": 28}, "end": {"line": 125, "column": 3}}, "locations": [{"start": {"line": 118, "column": 28}, "end": {"line": 125, "column": 3}}]}, "22": {"type": "branch", "line": 118, "loc": {"start": {"line": 118, "column": 28}, "end": {"line": 120, "column": 3}}, "locations": [{"start": {"line": 118, "column": 28}, "end": {"line": 120, "column": 3}}]}, "23": {"type": "branch", "line": 123, "loc": {"start": {"line": 123, "column": 28}, "end": {"line": 125, "column": 3}}, "locations": [{"start": {"line": 123, "column": 28}, "end": {"line": 125, "column": 3}}]}, "24": {"type": "branch", "line": 125, "loc": {"start": {"line": 125, "column": 2}, "end": {"line": 141, "column": 1}}, "locations": [{"start": {"line": 125, "column": 2}, "end": {"line": 141, "column": 1}}]}, "25": {"type": "branch", "line": 128, "loc": {"start": {"line": 128, "column": 28}, "end": {"line": 130, "column": 3}}, "locations": [{"start": {"line": 128, "column": 28}, "end": {"line": 130, "column": 3}}]}, "26": {"type": "branch", "line": 133, "loc": {"start": {"line": 133, "column": 31}, "end": {"line": 135, "column": 3}}, "locations": [{"start": {"line": 133, "column": 31}, "end": {"line": 135, "column": 3}}]}, "27": {"type": "branch", "line": 144, "loc": {"start": {"line": 144, "column": 0}, "end": {"line": 149, "column": 1}}, "locations": [{"start": {"line": 144, "column": 0}, "end": {"line": 149, "column": 1}}]}, "28": {"type": "branch", "line": 147, "loc": {"start": {"line": 147, "column": 167}, "end": {"line": 147, "column": 189}}, "locations": [{"start": {"line": 147, "column": 167}, "end": {"line": 147, "column": 189}}]}, "29": {"type": "branch", "line": 151, "loc": {"start": {"line": 151, "column": 0}, "end": {"line": 219, "column": 1}}, "locations": [{"start": {"line": 151, "column": 0}, "end": {"line": 219, "column": 1}}]}, "30": {"type": "branch", "line": 154, "loc": {"start": {"line": 154, "column": 36}, "end": {"line": 156, "column": 3}}, "locations": [{"start": {"line": 154, "column": 36}, "end": {"line": 156, "column": 3}}]}, "31": {"type": "branch", "line": 156, "loc": {"start": {"line": 156, "column": 2}, "end": {"line": 219, "column": 1}}, "locations": [{"start": {"line": 156, "column": 2}, "end": {"line": 219, "column": 1}}]}, "32": {"type": "branch", "line": 159, "loc": {"start": {"line": 159, "column": 39}, "end": {"line": 161, "column": 3}}, "locations": [{"start": {"line": 159, "column": 39}, "end": {"line": 161, "column": 3}}]}, "33": {"type": "branch", "line": 164, "loc": {"start": {"line": 164, "column": 37}, "end": {"line": 166, "column": 3}}, "locations": [{"start": {"line": 164, "column": 37}, "end": {"line": 166, "column": 3}}]}, "34": {"type": "branch", "line": 170, "loc": {"start": {"line": 170, "column": 28}, "end": {"line": 172, "column": 3}}, "locations": [{"start": {"line": 170, "column": 28}, "end": {"line": 172, "column": 3}}]}, "35": {"type": "branch", "line": 175, "loc": {"start": {"line": 175, "column": 29}, "end": {"line": 177, "column": 3}}, "locations": [{"start": {"line": 175, "column": 29}, "end": {"line": 177, "column": 3}}]}, "36": {"type": "branch", "line": 180, "loc": {"start": {"line": 180, "column": 28}, "end": {"line": 182, "column": 3}}, "locations": [{"start": {"line": 180, "column": 28}, "end": {"line": 182, "column": 3}}]}, "37": {"type": "branch", "line": 185, "loc": {"start": {"line": 185, "column": 28}, "end": {"line": 187, "column": 3}}, "locations": [{"start": {"line": 185, "column": 28}, "end": {"line": 187, "column": 3}}]}, "38": {"type": "branch", "line": 190, "loc": {"start": {"line": 190, "column": 28}, "end": {"line": 192, "column": 3}}, "locations": [{"start": {"line": 190, "column": 28}, "end": {"line": 192, "column": 3}}]}, "39": {"type": "branch", "line": 195, "loc": {"start": {"line": 195, "column": 31}, "end": {"line": 197, "column": 3}}, "locations": [{"start": {"line": 195, "column": 31}, "end": {"line": 197, "column": 3}}]}, "40": {"type": "branch", "line": 200, "loc": {"start": {"line": 200, "column": 31}, "end": {"line": 202, "column": 3}}, "locations": [{"start": {"line": 200, "column": 31}, "end": {"line": 202, "column": 3}}]}, "41": {"type": "branch", "line": 206, "loc": {"start": {"line": 206, "column": 61}, "end": {"line": 219, "column": 1}}, "locations": [{"start": {"line": 206, "column": 61}, "end": {"line": 219, "column": 1}}]}, "42": {"type": "branch", "line": 221, "loc": {"start": {"line": 221, "column": 0}, "end": {"line": 224, "column": 1}}, "locations": [{"start": {"line": 221, "column": 0}, "end": {"line": 224, "column": 1}}]}, "43": {"type": "branch", "line": 223, "loc": {"start": {"line": 223, "column": 98}, "end": {"line": 223, "column": 105}}, "locations": [{"start": {"line": 223, "column": 98}, "end": {"line": 223, "column": 105}}]}, "44": {"type": "branch", "line": 226, "loc": {"start": {"line": 226, "column": 0}, "end": {"line": 229, "column": 1}}, "locations": [{"start": {"line": 226, "column": 0}, "end": {"line": 229, "column": 1}}]}, "45": {"type": "branch", "line": 228, "loc": {"start": {"line": 228, "column": 64}, "end": {"line": 228, "column": 71}}, "locations": [{"start": {"line": 228, "column": 64}, "end": {"line": 228, "column": 71}}]}, "46": {"type": "branch", "line": 232, "loc": {"start": {"line": 232, "column": 0}, "end": {"line": 241, "column": 1}}, "locations": [{"start": {"line": 232, "column": 0}, "end": {"line": 241, "column": 1}}]}, "47": {"type": "branch", "line": 237, "loc": {"start": {"line": 237, "column": 12}, "end": {"line": 237, "column": 34}}, "locations": [{"start": {"line": 237, "column": 12}, "end": {"line": 237, "column": 34}}]}, "48": {"type": "branch", "line": 237, "loc": {"start": {"line": 237, "column": 22}, "end": {"line": 237, "column": 101}}, "locations": [{"start": {"line": 237, "column": 22}, "end": {"line": 237, "column": 101}}]}, "49": {"type": "branch", "line": 243, "loc": {"start": {"line": 243, "column": 0}, "end": {"line": 252, "column": 1}}, "locations": [{"start": {"line": 243, "column": 0}, "end": {"line": 252, "column": 1}}]}, "50": {"type": "branch", "line": 248, "loc": {"start": {"line": 248, "column": 12}, "end": {"line": 248, "column": 34}}, "locations": [{"start": {"line": 248, "column": 12}, "end": {"line": 248, "column": 34}}]}, "51": {"type": "branch", "line": 248, "loc": {"start": {"line": 248, "column": 22}, "end": {"line": 248, "column": 120}}, "locations": [{"start": {"line": 248, "column": 22}, "end": {"line": 248, "column": 120}}]}}, "b": {"0": [5], "1": [1], "2": [1], "3": [2], "4": [13], "5": [9], "6": [4], "7": [4], "8": [5], "9": [0], "10": [0], "11": [2], "12": [3], "13": [2], "14": [0], "15": [2], "16": [1], "17": [2], "18": [4], "19": [1], "20": [3], "21": [2], "22": [1], "23": [1], "24": [1], "25": [0], "26": [0], "27": [1], "28": [2], "29": [5], "30": [1], "31": [4], "32": [0], "33": [0], "34": [0], "35": [0], "36": [0], "37": [0], "38": [0], "39": [0], "40": [0], "41": [2], "42": [7], "43": [0], "44": [9], "45": [3], "46": [2], "47": [1], "48": [1], "49": [2], "50": [1], "51": [1]}, "fnMap": {"0": {"name": "FreeTierLimitError", "decl": {"start": {"line": 18, "column": 2}, "end": {"line": 21, "column": 3}}, "loc": {"start": {"line": 18, "column": 2}, "end": {"line": 21, "column": 3}}, "line": 18}, "1": {"name": "initDb", "decl": {"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 50}}, "loc": {"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 50}}, "line": 24}, "2": {"name": "listProjects", "decl": {"start": {"line": 26, "column": 0}, "end": {"line": 31, "column": 1}}, "loc": {"start": {"line": 26, "column": 0}, "end": {"line": 31, "column": 1}}, "line": 26}, "3": {"name": "callback", "decl": {"start": {"line": 29, "column": 125}, "end": {"line": 29, "column": 147}}, "loc": {"start": {"line": 29, "column": 125}, "end": {"line": 29, "column": 147}}, "line": 29}, "4": {"name": "createProject", "decl": {"start": {"line": 33, "column": 0}, "end": {"line": 100, "column": 1}}, "loc": {"start": {"line": 33, "column": 0}, "end": {"line": 100, "column": 1}}, "line": 33}, "5": {"name": "listJunctions", "decl": {"start": {"line": 103, "column": 0}, "end": {"line": 108, "column": 1}}, "loc": {"start": {"line": 103, "column": 0}, "end": {"line": 108, "column": 1}}, "line": 103}, "6": {"name": "callback", "decl": {"start": {"line": 106, "column": 139}, "end": {"line": 106, "column": 161}}, "loc": {"start": {"line": 106, "column": 139}, "end": {"line": 106, "column": 161}}, "line": 106}, "7": {"name": "createJunction", "decl": {"start": {"line": 110, "column": 0}, "end": {"line": 141, "column": 1}}, "loc": {"start": {"line": 110, "column": 0}, "end": {"line": 141, "column": 1}}, "line": 110}, "8": {"name": "listSegments", "decl": {"start": {"line": 144, "column": 0}, "end": {"line": 149, "column": 1}}, "loc": {"start": {"line": 144, "column": 0}, "end": {"line": 149, "column": 1}}, "line": 144}, "9": {"name": "callback", "decl": {"start": {"line": 147, "column": 167}, "end": {"line": 147, "column": 189}}, "loc": {"start": {"line": 147, "column": 167}, "end": {"line": 147, "column": 189}}, "line": 147}, "10": {"name": "createSegment", "decl": {"start": {"line": 151, "column": 0}, "end": {"line": 219, "column": 1}}, "loc": {"start": {"line": 151, "column": 0}, "end": {"line": 219, "column": 1}}, "line": 151}, "11": {"name": "countSegments", "decl": {"start": {"line": 221, "column": 0}, "end": {"line": 224, "column": 1}}, "loc": {"start": {"line": 221, "column": 0}, "end": {"line": 224, "column": 1}}, "line": 221}, "12": {"name": "countProjects", "decl": {"start": {"line": 226, "column": 0}, "end": {"line": 229, "column": 1}}, "loc": {"start": {"line": 226, "column": 0}, "end": {"line": 229, "column": 1}}, "line": 226}, "13": {"name": "canCreateProject", "decl": {"start": {"line": 232, "column": 0}, "end": {"line": 241, "column": 1}}, "loc": {"start": {"line": 232, "column": 0}, "end": {"line": 241, "column": 1}}, "line": 232}, "14": {"name": "canCreateSegment", "decl": {"start": {"line": 243, "column": 0}, "end": {"line": 252, "column": 1}}, "loc": {"start": {"line": 243, "column": 0}, "end": {"line": 252, "column": 1}}, "line": 243}, "15": {"name": "getProjectData", "decl": {"start": {"line": 255, "column": 0}, "end": {"line": 275, "column": 1}}, "loc": {"start": {"line": 255, "column": 0}, "end": {"line": 275, "column": 1}}, "line": 255}, "16": {"name": "createProjectSnapshot", "decl": {"start": {"line": 278, "column": 0}, "end": {"line": 291, "column": 1}}, "loc": {"start": {"line": 278, "column": 0}, "end": {"line": 291, "column": 1}}, "line": 278}}, "f": {"0": 5, "1": 1, "2": 1, "3": 2, "4": 13, "5": 1, "6": 2, "7": 4, "8": 1, "9": 2, "10": 5, "11": 7, "12": 9, "13": 2, "14": 2, "15": 0, "16": 0}}, "/Users/<USER>/Documents/SizeWise_Offline_App/db/migrations.ts": {"path": "/Users/<USER>/Documents/SizeWise_Offline_App/db/migrations.ts", "all": false, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 34}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 32}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 28}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 102}}, "5": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 71}}, "6": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 90}}, "7": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 20}}, "8": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": 37}}, "9": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": 22}}, "10": {"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 12}}, "11": {"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 17}}, "12": {"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 5}}, "13": {"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 86}}, "14": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 4}}, "16": {"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 21}}, "72": {"start": {"line": 73, "column": 0}, "end": {"line": 73, "column": 5}}, "74": {"start": {"line": 75, "column": 0}, "end": {"line": 75, "column": 37}}, "78": {"start": {"line": 79, "column": 0}, "end": {"line": 79, "column": 5}}, "80": {"start": {"line": 81, "column": 0}, "end": {"line": 81, "column": 34}}, "111": {"start": {"line": 112, "column": 0}, "end": {"line": 112, "column": 5}}, "114": {"start": {"line": 115, "column": 0}, "end": {"line": 115, "column": 21}}, "151": {"start": {"line": 152, "column": 0}, "end": {"line": 152, "column": 5}}, "154": {"start": {"line": 155, "column": 0}, "end": {"line": 155, "column": 42}}, "155": {"start": {"line": 156, "column": 0}, "end": {"line": 156, "column": 85}}, "156": {"start": {"line": 157, "column": 0}, "end": {"line": 157, "column": 53}}, "157": {"start": {"line": 158, "column": 0}, "end": {"line": 158, "column": 31}}, "158": {"start": {"line": 159, "column": 0}, "end": {"line": 159, "column": 9}}, "159": {"start": {"line": 160, "column": 0}, "end": {"line": 160, "column": 123}}, "160": {"start": {"line": 161, "column": 0}, "end": {"line": 161, "column": 104}}, "161": {"start": {"line": 162, "column": 0}, "end": {"line": 162, "column": 24}}, "162": {"start": {"line": 163, "column": 0}, "end": {"line": 163, "column": 17}}, "163": {"start": {"line": 164, "column": 0}, "end": {"line": 164, "column": 26}}, "164": {"start": {"line": 165, "column": 0}, "end": {"line": 165, "column": 14}}, "165": {"start": {"line": 166, "column": 0}, "end": {"line": 166, "column": 5}}, "166": {"start": {"line": 167, "column": 0}, "end": {"line": 167, "column": 5}}, "167": {"start": {"line": 168, "column": 0}, "end": {"line": 168, "column": 1}}}, "s": {"0": 1, "2": 15, "3": 15, "4": 14, "5": 14, "6": 61, "7": 61, "8": 61, "9": 45, "10": 61, "11": 11, "12": 11, "13": 56, "14": 61, "16": 14, "72": 14, "74": 14, "78": 14, "80": 14, "111": 14, "114": 14, "151": 14, "154": 14, "155": 11, "156": 11, "157": 11, "158": 11, "159": 11, "160": 11, "161": 11, "162": 11, "163": 0, "164": 0, "165": 0, "166": 14, "167": 14}, "branchMap": {"0": {"type": "branch", "line": 3, "loc": {"start": {"line": 3, "column": 0}, "end": {"line": 168, "column": 1}}, "locations": [{"start": {"line": 3, "column": 0}, "end": {"line": 168, "column": 1}}]}, "1": {"type": "branch", "line": 4, "loc": {"start": {"line": 4, "column": 26}, "end": {"line": 168, "column": 1}}, "locations": [{"start": {"line": 4, "column": 26}, "end": {"line": 168, "column": 1}}]}, "2": {"type": "branch", "line": 6, "loc": {"start": {"line": 6, "column": 16}, "end": {"line": 15, "column": 4}}, "locations": [{"start": {"line": 6, "column": 16}, "end": {"line": 15, "column": 4}}]}, "3": {"type": "branch", "line": 8, "loc": {"start": {"line": 8, "column": 13}, "end": {"line": 8, "column": 20}}, "locations": [{"start": {"line": 8, "column": 13}, "end": {"line": 8, "column": 20}}]}, "4": {"type": "branch", "line": 8, "loc": {"start": {"line": 8, "column": 13}, "end": {"line": 9, "column": 36}}, "locations": [{"start": {"line": 8, "column": 13}, "end": {"line": 9, "column": 36}}]}, "5": {"type": "branch", "line": 9, "loc": {"start": {"line": 9, "column": 36}, "end": {"line": 11, "column": 11}}, "locations": [{"start": {"line": 9, "column": 36}, "end": {"line": 11, "column": 11}}]}, "6": {"type": "branch", "line": 11, "loc": {"start": {"line": 11, "column": 4}, "end": {"line": 13, "column": 5}}, "locations": [{"start": {"line": 11, "column": 4}, "end": {"line": 13, "column": 5}}]}, "7": {"type": "branch", "line": 13, "loc": {"start": {"line": 13, "column": 4}, "end": {"line": 14, "column": 86}}, "locations": [{"start": {"line": 13, "column": 4}, "end": {"line": 14, "column": 86}}]}, "8": {"type": "branch", "line": 155, "loc": {"start": {"line": 155, "column": 28}, "end": {"line": 167, "column": 3}}, "locations": [{"start": {"line": 155, "column": 28}, "end": {"line": 167, "column": 3}}]}, "9": {"type": "branch", "line": 156, "loc": {"start": {"line": 156, "column": 77}, "end": {"line": 156, "column": 85}}, "locations": [{"start": {"line": 156, "column": 77}, "end": {"line": 156, "column": 85}}]}, "10": {"type": "branch", "line": 163, "loc": {"start": {"line": 163, "column": 4}, "end": {"line": 166, "column": 5}}, "locations": [{"start": {"line": 163, "column": 4}, "end": {"line": 166, "column": 5}}]}}, "b": {"0": [15], "1": [14], "2": [61], "3": [5], "4": [56], "5": [45], "6": [11], "7": [56], "8": [11], "9": [0], "10": [0]}, "fnMap": {"0": {"name": "migrate", "decl": {"start": {"line": 3, "column": 0}, "end": {"line": 168, "column": 1}}, "loc": {"start": {"line": 3, "column": 0}, "end": {"line": 168, "column": 1}}, "line": 3}, "1": {"name": "apply", "decl": {"start": {"line": 6, "column": 16}, "end": {"line": 15, "column": 4}}, "loc": {"start": {"line": 6, "column": 16}, "end": {"line": 15, "column": 4}}, "line": 6}}, "f": {"0": 15, "1": 61}}, "/Users/<USER>/Documents/SizeWise_Offline_App/db/openDb.ts": {"path": "/Users/<USER>/Documents/SizeWise_Offline_App/db/openDb.ts", "all": false, "statementMap": {"4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 41}}, "6": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 45}}, "7": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 34}}, "8": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": 28}}, "11": {"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 42}}, "13": {"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 73}}, "14": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 63}}, "15": {"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 7}}, "18": {"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 31}}, "19": {"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 6}}, "21": {"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 76}}, "22": {"start": {"line": 23, "column": 0}, "end": {"line": 23, "column": 53}}, "23": {"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 67}}, "24": {"start": {"line": 25, "column": 0}, "end": {"line": 25, "column": 14}}, "25": {"start": {"line": 26, "column": 0}, "end": {"line": 26, "column": 7}}, "26": {"start": {"line": 27, "column": 0}, "end": {"line": 27, "column": 19}}, "27": {"start": {"line": 28, "column": 0}, "end": {"line": 28, "column": 1}}}, "s": {"4": 1, "6": 0, "7": 0, "8": 0, "11": 0, "13": 0, "14": 0, "15": 0, "18": 0, "19": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0}, "branchMap": {}, "b": {}, "fnMap": {"0": {"name": "openDb", "decl": {"start": {"line": 7, "column": 0}, "end": {"line": 28, "column": 1}}, "loc": {"start": {"line": 7, "column": 0}, "end": {"line": 28, "column": 1}}, "line": 7}}, "f": {"0": 0}}, "/Users/<USER>/Documents/SizeWise_Offline_App/lib/featureFlags.ts": {"path": "/Users/<USER>/Documents/SizeWise_Offline_App/lib/featureFlags.ts", "all": false, "statementMap": {"1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 48}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 7}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 51}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 31}}, "5": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 25}}, "6": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 1}}, "7": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 51}}, "8": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": 72}}, "9": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": 1}}, "12": {"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 30}}, "13": {"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 46}}, "14": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 44}}, "15": {"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 52}}, "16": {"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 37}}, "17": {"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 33}}, "18": {"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 11}}, "21": {"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 53}}, "22": {"start": {"line": 23, "column": 0}, "end": {"line": 23, "column": 84}}, "23": {"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 1}}, "25": {"start": {"line": 26, "column": 0}, "end": {"line": 26, "column": 51}}, "26": {"start": {"line": 27, "column": 0}, "end": {"line": 27, "column": 70}}, "27": {"start": {"line": 28, "column": 0}, "end": {"line": 28, "column": 1}}, "29": {"start": {"line": 30, "column": 0}, "end": {"line": 30, "column": 55}}, "30": {"start": {"line": 31, "column": 0}, "end": {"line": 31, "column": 74}}, "31": {"start": {"line": 32, "column": 0}, "end": {"line": 32, "column": 1}}, "33": {"start": {"line": 34, "column": 0}, "end": {"line": 34, "column": 51}}, "34": {"start": {"line": 35, "column": 0}, "end": {"line": 35, "column": 88}}, "35": {"start": {"line": 36, "column": 0}, "end": {"line": 36, "column": 1}}, "37": {"start": {"line": 38, "column": 0}, "end": {"line": 38, "column": 50}}, "38": {"start": {"line": 39, "column": 0}, "end": {"line": 39, "column": 73}}, "39": {"start": {"line": 40, "column": 0}, "end": {"line": 40, "column": 1}}, "42": {"start": {"line": 43, "column": 0}, "end": {"line": 43, "column": 42}}, "43": {"start": {"line": 44, "column": 0}, "end": {"line": 44, "column": 7}}, "45": {"start": {"line": 46, "column": 0}, "end": {"line": 46, "column": 82}}, "46": {"start": {"line": 47, "column": 0}, "end": {"line": 47, "column": 52}}, "47": {"start": {"line": 48, "column": 0}, "end": {"line": 48, "column": 5}}, "48": {"start": {"line": 49, "column": 0}, "end": {"line": 49, "column": 81}}, "49": {"start": {"line": 50, "column": 0}, "end": {"line": 50, "column": 51}}, "50": {"start": {"line": 51, "column": 0}, "end": {"line": 51, "column": 5}}, "51": {"start": {"line": 52, "column": 0}, "end": {"line": 52, "column": 85}}, "52": {"start": {"line": 53, "column": 0}, "end": {"line": 53, "column": 55}}, "53": {"start": {"line": 54, "column": 0}, "end": {"line": 54, "column": 5}}, "54": {"start": {"line": 55, "column": 0}, "end": {"line": 55, "column": 11}}, "56": {"start": {"line": 57, "column": 0}, "end": {"line": 57, "column": 3}}, "57": {"start": {"line": 58, "column": 0}, "end": {"line": 58, "column": 1}}}, "s": {"1": 1, "2": 28, "3": 28, "4": 28, "5": 28, "6": 28, "7": 1, "8": 15, "9": 15, "12": 1, "13": 1, "14": 1, "15": 1, "16": 1, "17": 1, "18": 1, "21": 1, "22": 7, "23": 7, "25": 1, "26": 3, "27": 3, "29": 1, "30": 3, "31": 3, "33": 1, "34": 0, "35": 0, "37": 1, "38": 0, "39": 0, "42": 1, "43": 3, "45": 3, "46": 1, "47": 1, "48": 3, "49": 1, "50": 1, "51": 2, "52": 2, "53": 2, "54": 3, "56": 1, "57": 3}, "branchMap": {"0": {"type": "branch", "line": 2, "loc": {"start": {"line": 2, "column": 7}, "end": {"line": 7, "column": 1}}, "locations": [{"start": {"line": 2, "column": 7}, "end": {"line": 7, "column": 1}}]}, "1": {"type": "branch", "line": 5, "loc": {"start": {"line": 5, "column": 11}, "end": {"line": 5, "column": 27}}, "locations": [{"start": {"line": 5, "column": 11}, "end": {"line": 5, "column": 27}}]}, "2": {"type": "branch", "line": 5, "loc": {"start": {"line": 5, "column": 21}, "end": {"line": 5, "column": 31}}, "locations": [{"start": {"line": 5, "column": 21}, "end": {"line": 5, "column": 31}}]}, "3": {"type": "branch", "line": 6, "loc": {"start": {"line": 6, "column": 2}, "end": {"line": 6, "column": 25}}, "locations": [{"start": {"line": 6, "column": 2}, "end": {"line": 6, "column": 25}}]}, "4": {"type": "branch", "line": 8, "loc": {"start": {"line": 8, "column": 7}, "end": {"line": 10, "column": 1}}, "locations": [{"start": {"line": 8, "column": 7}, "end": {"line": 10, "column": 1}}]}, "5": {"type": "branch", "line": 9, "loc": {"start": {"line": 9, "column": 45}, "end": {"line": 9, "column": 56}}, "locations": [{"start": {"line": 9, "column": 45}, "end": {"line": 9, "column": 56}}]}, "6": {"type": "branch", "line": 9, "loc": {"start": {"line": 9, "column": 50}, "end": {"line": 9, "column": 59}}, "locations": [{"start": {"line": 9, "column": 50}, "end": {"line": 9, "column": 59}}]}, "7": {"type": "branch", "line": 9, "loc": {"start": {"line": 9, "column": 62}, "end": {"line": 9, "column": 72}}, "locations": [{"start": {"line": 9, "column": 62}, "end": {"line": 9, "column": 72}}]}, "8": {"type": "branch", "line": 22, "loc": {"start": {"line": 22, "column": 7}, "end": {"line": 24, "column": 1}}, "locations": [{"start": {"line": 22, "column": 7}, "end": {"line": 24, "column": 1}}]}, "9": {"type": "branch", "line": 26, "loc": {"start": {"line": 26, "column": 7}, "end": {"line": 28, "column": 1}}, "locations": [{"start": {"line": 26, "column": 7}, "end": {"line": 28, "column": 1}}]}, "10": {"type": "branch", "line": 30, "loc": {"start": {"line": 30, "column": 7}, "end": {"line": 32, "column": 1}}, "locations": [{"start": {"line": 30, "column": 7}, "end": {"line": 32, "column": 1}}]}, "11": {"type": "branch", "line": 43, "loc": {"start": {"line": 43, "column": 7}, "end": {"line": 58, "column": 1}}, "locations": [{"start": {"line": 43, "column": 7}, "end": {"line": 58, "column": 1}}]}, "12": {"type": "branch", "line": 46, "loc": {"start": {"line": 46, "column": 81}, "end": {"line": 48, "column": 5}}, "locations": [{"start": {"line": 46, "column": 81}, "end": {"line": 48, "column": 5}}]}, "13": {"type": "branch", "line": 48, "loc": {"start": {"line": 48, "column": 4}, "end": {"line": 49, "column": 80}}, "locations": [{"start": {"line": 48, "column": 4}, "end": {"line": 49, "column": 80}}]}, "14": {"type": "branch", "line": 49, "loc": {"start": {"line": 49, "column": 80}, "end": {"line": 51, "column": 5}}, "locations": [{"start": {"line": 49, "column": 80}, "end": {"line": 51, "column": 5}}]}, "15": {"type": "branch", "line": 51, "loc": {"start": {"line": 51, "column": 4}, "end": {"line": 54, "column": 5}}, "locations": [{"start": {"line": 51, "column": 4}, "end": {"line": 54, "column": 5}}]}, "16": {"type": "branch", "line": 55, "loc": {"start": {"line": 55, "column": 2}, "end": {"line": 57, "column": 3}}, "locations": [{"start": {"line": 55, "column": 2}, "end": {"line": 57, "column": 3}}]}}, "b": {"0": [28], "1": [17], "2": [9], "3": [2], "4": [15], "5": [9], "6": [6], "7": [1], "8": [7], "9": [3], "10": [3], "11": [3], "12": [1], "13": [2], "14": [1], "15": [2], "16": [1]}, "fnMap": {"0": {"name": "flag", "decl": {"start": {"line": 2, "column": 7}, "end": {"line": 7, "column": 1}}, "loc": {"start": {"line": 2, "column": 7}, "end": {"line": 7, "column": 1}}, "line": 2}, "1": {"name": "setFlag", "decl": {"start": {"line": 8, "column": 7}, "end": {"line": 10, "column": 1}}, "loc": {"start": {"line": 8, "column": 7}, "end": {"line": 10, "column": 1}}, "line": 8}, "2": {"name": "isVaultEncryptionEnabled", "decl": {"start": {"line": 22, "column": 7}, "end": {"line": 24, "column": 1}}, "loc": {"start": {"line": 22, "column": 7}, "end": {"line": 24, "column": 1}}, "line": 22}, "3": {"name": "isFreeTierGuardEnabled", "decl": {"start": {"line": 26, "column": 7}, "end": {"line": 28, "column": 1}}, "loc": {"start": {"line": 26, "column": 7}, "end": {"line": 28, "column": 1}}, "line": 26}, "4": {"name": "isProjectAutoBackupEnabled", "decl": {"start": {"line": 30, "column": 7}, "end": {"line": 32, "column": 1}}, "loc": {"start": {"line": 30, "column": 7}, "end": {"line": 32, "column": 1}}, "line": 30}, "5": {"name": "isAuthGateVaultEnabled", "decl": {"start": {"line": 34, "column": 7}, "end": {"line": 36, "column": 1}}, "loc": {"start": {"line": 34, "column": 7}, "end": {"line": 36, "column": 1}}, "line": 34}, "6": {"name": "isAuthWebAuthnEnabled", "decl": {"start": {"line": 38, "column": 7}, "end": {"line": 40, "column": 1}}, "loc": {"start": {"line": 38, "column": 7}, "end": {"line": 40, "column": 1}}, "line": 38}, "7": {"name": "initializeDefaultFlags", "decl": {"start": {"line": 43, "column": 7}, "end": {"line": 58, "column": 1}}, "loc": {"start": {"line": 43, "column": 7}, "end": {"line": 58, "column": 1}}, "line": 43}}, "f": {"0": 28, "1": 15, "2": 7, "3": 3, "4": 3, "5": 0, "6": 0, "7": 3}}, "/Users/<USER>/Documents/SizeWise_Offline_App/lib/ids.ts": {"path": "/Users/<USER>/Documents/SizeWise_Offline_App/lib/ids.ts", "all": false, "statementMap": {"1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 32}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 52}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 13}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 72}}, "5": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 30}}, "6": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 1}}}, "s": {"1": 1, "2": 1132, "3": 1132, "4": 1132, "5": 1132, "6": 1132}, "branchMap": {"0": {"type": "branch", "line": 2, "loc": {"start": {"line": 2, "column": 7}, "end": {"line": 7, "column": 1}}, "locations": [{"start": {"line": 2, "column": 7}, "end": {"line": 7, "column": 1}}]}, "1": {"type": "branch", "line": 5, "loc": {"start": {"line": 5, "column": 25}, "end": {"line": 5, "column": 72}}, "locations": [{"start": {"line": 5, "column": 25}, "end": {"line": 5, "column": 72}}]}}, "b": {"0": [1132], "1": [18112]}, "fnMap": {"0": {"name": "ulid", "decl": {"start": {"line": 2, "column": 7}, "end": {"line": 7, "column": 1}}, "loc": {"start": {"line": 2, "column": 7}, "end": {"line": 7, "column": 1}}, "line": 2}}, "f": {"0": 1132}}, "/Users/<USER>/Documents/SizeWise_Offline_App/lib/inputValidation.ts": {"path": "/Users/<USER>/Documents/SizeWise_Offline_App/lib/inputValidation.ts", "all": false, "statementMap": {"11": {"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 29}}, "12": {"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 20}}, "13": {"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 27}}, "14": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 13}}, "15": {"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 14}}, "16": {"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 20}}, "17": {"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 11}}, "20": {"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 53}}, "21": {"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 43}}, "23": {"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 14}}, "24": {"start": {"line": 25, "column": 0}, "end": {"line": 25, "column": 93}}, "25": {"start": {"line": 26, "column": 0}, "end": {"line": 26, "column": 52}}, "26": {"start": {"line": 27, "column": 0}, "end": {"line": 27, "column": 64}}, "27": {"start": {"line": 28, "column": 0}, "end": {"line": 28, "column": 85}}, "28": {"start": {"line": 29, "column": 0}, "end": {"line": 29, "column": 83}}, "29": {"start": {"line": 30, "column": 0}, "end": {"line": 30, "column": 52}}, "30": {"start": {"line": 31, "column": 0}, "end": {"line": 31, "column": 12}}, "31": {"start": {"line": 32, "column": 0}, "end": {"line": 32, "column": 1}}, "34": {"start": {"line": 35, "column": 0}, "end": {"line": 35, "column": 69}}, "35": {"start": {"line": 36, "column": 0}, "end": {"line": 36, "column": 33}}, "36": {"start": {"line": 37, "column": 0}, "end": {"line": 37, "column": 70}}, "37": {"start": {"line": 38, "column": 0}, "end": {"line": 38, "column": 3}}, "39": {"start": {"line": 40, "column": 0}, "end": {"line": 40, "column": 30}}, "41": {"start": {"line": 42, "column": 0}, "end": {"line": 42, "column": 17}}, "42": {"start": {"line": 43, "column": 0}, "end": {"line": 43, "column": 65}}, "43": {"start": {"line": 44, "column": 0}, "end": {"line": 44, "column": 3}}, "45": {"start": {"line": 46, "column": 0}, "end": {"line": 46, "column": 51}}, "46": {"start": {"line": 47, "column": 0}, "end": {"line": 47, "column": 13}}, "47": {"start": {"line": 48, "column": 0}, "end": {"line": 48, "column": 22}}, "48": {"start": {"line": 49, "column": 0}, "end": {"line": 49, "column": 84}}, "49": {"start": {"line": 50, "column": 0}, "end": {"line": 50, "column": 6}}, "50": {"start": {"line": 51, "column": 0}, "end": {"line": 51, "column": 3}}, "53": {"start": {"line": 54, "column": 0}, "end": {"line": 54, "column": 34}}, "54": {"start": {"line": 55, "column": 0}, "end": {"line": 55, "column": 12}}, "55": {"start": {"line": 56, "column": 0}, "end": {"line": 56, "column": 21}}, "56": {"start": {"line": 57, "column": 0}, "end": {"line": 57, "column": 55}}, "57": {"start": {"line": 58, "column": 0}, "end": {"line": 58, "column": 6}}, "58": {"start": {"line": 59, "column": 0}, "end": {"line": 59, "column": 3}}, "60": {"start": {"line": 61, "column": 0}, "end": {"line": 61, "column": 42}}, "61": {"start": {"line": 62, "column": 0}, "end": {"line": 62, "column": 38}}, "62": {"start": {"line": 63, "column": 0}, "end": {"line": 63, "column": 1}}, "65": {"start": {"line": 66, "column": 0}, "end": {"line": 66, "column": 83}}, "66": {"start": {"line": 67, "column": 0}, "end": {"line": 67, "column": 40}}, "67": {"start": {"line": 68, "column": 0}, "end": {"line": 68, "column": 69}}, "68": {"start": {"line": 69, "column": 0}, "end": {"line": 69, "column": 3}}, "70": {"start": {"line": 71, "column": 0}, "end": {"line": 71, "column": 37}}, "72": {"start": {"line": 73, "column": 0}, "end": {"line": 73, "column": 58}}, "73": {"start": {"line": 74, "column": 0}, "end": {"line": 74, "column": 12}}, "74": {"start": {"line": 75, "column": 0}, "end": {"line": 75, "column": 21}}, "75": {"start": {"line": 76, "column": 0}, "end": {"line": 76, "column": 89}}, "76": {"start": {"line": 77, "column": 0}, "end": {"line": 77, "column": 6}}, "77": {"start": {"line": 78, "column": 0}, "end": {"line": 78, "column": 3}}, "80": {"start": {"line": 81, "column": 0}, "end": {"line": 81, "column": 34}}, "81": {"start": {"line": 82, "column": 0}, "end": {"line": 82, "column": 12}}, "82": {"start": {"line": 83, "column": 0}, "end": {"line": 83, "column": 21}}, "83": {"start": {"line": 84, "column": 0}, "end": {"line": 84, "column": 54}}, "84": {"start": {"line": 85, "column": 0}, "end": {"line": 85, "column": 6}}, "85": {"start": {"line": 86, "column": 0}, "end": {"line": 86, "column": 3}}, "87": {"start": {"line": 88, "column": 0}, "end": {"line": 88, "column": 42}}, "88": {"start": {"line": 89, "column": 0}, "end": {"line": 89, "column": 38}}, "89": {"start": {"line": 90, "column": 0}, "end": {"line": 90, "column": 1}}, "92": {"start": {"line": 93, "column": 0}, "end": {"line": 93, "column": 60}}, "93": {"start": {"line": 94, "column": 0}, "end": {"line": 94, "column": 32}}, "94": {"start": {"line": 95, "column": 0}, "end": {"line": 95, "column": 61}}, "95": {"start": {"line": 96, "column": 0}, "end": {"line": 96, "column": 3}}, "98": {"start": {"line": 99, "column": 0}, "end": {"line": 99, "column": 45}}, "100": {"start": {"line": 101, "column": 0}, "end": {"line": 101, "column": 50}}, "101": {"start": {"line": 102, "column": 0}, "end": {"line": 102, "column": 13}}, "102": {"start": {"line": 103, "column": 0}, "end": {"line": 103, "column": 22}}, "103": {"start": {"line": 104, "column": 0}, "end": {"line": 104, "column": 67}}, "104": {"start": {"line": 105, "column": 0}, "end": {"line": 105, "column": 6}}, "105": {"start": {"line": 106, "column": 0}, "end": {"line": 106, "column": 3}}, "107": {"start": {"line": 108, "column": 0}, "end": {"line": 108, "column": 50}}, "108": {"start": {"line": 109, "column": 0}, "end": {"line": 109, "column": 13}}, "109": {"start": {"line": 110, "column": 0}, "end": {"line": 110, "column": 22}}, "110": {"start": {"line": 111, "column": 0}, "end": {"line": 111, "column": 66}}, "111": {"start": {"line": 112, "column": 0}, "end": {"line": 112, "column": 6}}, "112": {"start": {"line": 113, "column": 0}, "end": {"line": 113, "column": 3}}, "114": {"start": {"line": 115, "column": 0}, "end": {"line": 115, "column": 51}}, "115": {"start": {"line": 116, "column": 0}, "end": {"line": 116, "column": 1}}, "118": {"start": {"line": 119, "column": 0}, "end": {"line": 119, "column": 70}}, "119": {"start": {"line": 120, "column": 0}, "end": {"line": 120, "column": 70}}, "121": {"start": {"line": 122, "column": 0}, "end": {"line": 122, "column": 44}}, "122": {"start": {"line": 123, "column": 0}, "end": {"line": 123, "column": 13}}, "123": {"start": {"line": 124, "column": 0}, "end": {"line": 124, "column": 22}}, "124": {"start": {"line": 125, "column": 0}, "end": {"line": 125, "column": 42}}, "125": {"start": {"line": 126, "column": 0}, "end": {"line": 126, "column": 6}}, "126": {"start": {"line": 127, "column": 0}, "end": {"line": 127, "column": 3}}, "128": {"start": {"line": 129, "column": 0}, "end": {"line": 129, "column": 48}}, "129": {"start": {"line": 130, "column": 0}, "end": {"line": 130, "column": 1}}, "132": {"start": {"line": 133, "column": 0}, "end": {"line": 133, "column": 74}}, "133": {"start": {"line": 134, "column": 0}, "end": {"line": 134, "column": 40}}, "135": {"start": {"line": 136, "column": 0}, "end": {"line": 136, "column": 41}}, "136": {"start": {"line": 137, "column": 0}, "end": {"line": 137, "column": 13}}, "137": {"start": {"line": 138, "column": 0}, "end": {"line": 138, "column": 22}}, "138": {"start": {"line": 139, "column": 0}, "end": {"line": 139, "column": 45}}, "139": {"start": {"line": 140, "column": 0}, "end": {"line": 140, "column": 6}}, "140": {"start": {"line": 141, "column": 0}, "end": {"line": 141, "column": 3}}, "142": {"start": {"line": 143, "column": 0}, "end": {"line": 143, "column": 50}}, "143": {"start": {"line": 144, "column": 0}, "end": {"line": 144, "column": 1}}, "146": {"start": {"line": 147, "column": 0}, "end": {"line": 147, "column": 103}}, "148": {"start": {"line": 149, "column": 0}, "end": {"line": 149, "column": 34}}, "150": {"start": {"line": 151, "column": 0}, "end": {"line": 151, "column": 50}}, "151": {"start": {"line": 152, "column": 0}, "end": {"line": 152, "column": 85}}, "152": {"start": {"line": 153, "column": 0}, "end": {"line": 153, "column": 71}}, "153": {"start": {"line": 154, "column": 0}, "end": {"line": 154, "column": 5}}, "154": {"start": {"line": 155, "column": 0}, "end": {"line": 155, "column": 3}}, "156": {"start": {"line": 157, "column": 0}, "end": {"line": 157, "column": 68}}, "158": {"start": {"line": 159, "column": 0}, "end": {"line": 159, "column": 37}}, "159": {"start": {"line": 160, "column": 0}, "end": {"line": 160, "column": 69}}, "160": {"start": {"line": 161, "column": 0}, "end": {"line": 161, "column": 3}}, "162": {"start": {"line": 163, "column": 0}, "end": {"line": 163, "column": 39}}, "163": {"start": {"line": 164, "column": 0}, "end": {"line": 164, "column": 70}}, "164": {"start": {"line": 165, "column": 0}, "end": {"line": 165, "column": 3}}, "166": {"start": {"line": 167, "column": 0}, "end": {"line": 167, "column": 39}}, "167": {"start": {"line": 168, "column": 0}, "end": {"line": 168, "column": 69}}, "168": {"start": {"line": 169, "column": 0}, "end": {"line": 169, "column": 3}}, "170": {"start": {"line": 171, "column": 0}, "end": {"line": 171, "column": 54}}, "171": {"start": {"line": 172, "column": 0}, "end": {"line": 172, "column": 1}}, "174": {"start": {"line": 175, "column": 0}, "end": {"line": 175, "column": 60}}, "175": {"start": {"line": 176, "column": 0}, "end": {"line": 176, "column": 31}}, "176": {"start": {"line": 177, "column": 0}, "end": {"line": 177, "column": 60}}, "177": {"start": {"line": 178, "column": 0}, "end": {"line": 178, "column": 3}}, "180": {"start": {"line": 181, "column": 0}, "end": {"line": 181, "column": 40}}, "182": {"start": {"line": 183, "column": 0}, "end": {"line": 183, "column": 30}}, "183": {"start": {"line": 184, "column": 0}, "end": {"line": 184, "column": 58}}, "184": {"start": {"line": 185, "column": 0}, "end": {"line": 185, "column": 3}}, "186": {"start": {"line": 187, "column": 0}, "end": {"line": 187, "column": 56}}, "187": {"start": {"line": 188, "column": 0}, "end": {"line": 188, "column": 1}}, "190": {"start": {"line": 191, "column": 0}, "end": {"line": 191, "column": 116}}, "191": {"start": {"line": 192, "column": 0}, "end": {"line": 192, "column": 33}}, "192": {"start": {"line": 193, "column": 0}, "end": {"line": 193, "column": 62}}, "193": {"start": {"line": 194, "column": 0}, "end": {"line": 194, "column": 3}}, "195": {"start": {"line": 196, "column": 0}, "end": {"line": 196, "column": 30}}, "197": {"start": {"line": 198, "column": 0}, "end": {"line": 198, "column": 35}}, "198": {"start": {"line": 199, "column": 0}, "end": {"line": 199, "column": 13}}, "199": {"start": {"line": 200, "column": 0}, "end": {"line": 200, "column": 22}}, "200": {"start": {"line": 201, "column": 0}, "end": {"line": 201, "column": 60}}, "201": {"start": {"line": 202, "column": 0}, "end": {"line": 202, "column": 6}}, "202": {"start": {"line": 203, "column": 0}, "end": {"line": 203, "column": 3}}, "204": {"start": {"line": 205, "column": 0}, "end": {"line": 205, "column": 42}}, "205": {"start": {"line": 206, "column": 0}, "end": {"line": 206, "column": 38}}, "206": {"start": {"line": 207, "column": 0}, "end": {"line": 207, "column": 1}}, "209": {"start": {"line": 210, "column": 0}, "end": {"line": 210, "column": 63}}, "210": {"start": {"line": 211, "column": 0}, "end": {"line": 211, "column": 7}}, "211": {"start": {"line": 212, "column": 0}, "end": {"line": 212, "column": 44}}, "214": {"start": {"line": 215, "column": 0}, "end": {"line": 215, "column": 36}}, "215": {"start": {"line": 216, "column": 0}, "end": {"line": 216, "column": 57}}, "216": {"start": {"line": 217, "column": 0}, "end": {"line": 217, "column": 5}}, "218": {"start": {"line": 219, "column": 0}, "end": {"line": 219, "column": 52}}, "219": {"start": {"line": 220, "column": 0}, "end": {"line": 220, "column": 19}}, "220": {"start": {"line": 221, "column": 0}, "end": {"line": 221, "column": 58}}, "221": {"start": {"line": 222, "column": 0}, "end": {"line": 222, "column": 3}}, "222": {"start": {"line": 223, "column": 0}, "end": {"line": 223, "column": 1}}, "225": {"start": {"line": 226, "column": 0}, "end": {"line": 226, "column": 26}}, "226": {"start": {"line": 227, "column": 0}, "end": {"line": 227, "column": 79}}, "227": {"start": {"line": 228, "column": 0}, "end": {"line": 228, "column": 39}}, "228": {"start": {"line": 229, "column": 0}, "end": {"line": 229, "column": 36}}, "230": {"start": {"line": 231, "column": 0}, "end": {"line": 231, "column": 75}}, "231": {"start": {"line": 232, "column": 0}, "end": {"line": 232, "column": 35}}, "232": {"start": {"line": 233, "column": 0}, "end": {"line": 233, "column": 29}}, "233": {"start": {"line": 234, "column": 0}, "end": {"line": 234, "column": 3}}, "235": {"start": {"line": 236, "column": 0}, "end": {"line": 236, "column": 35}}, "236": {"start": {"line": 237, "column": 0}, "end": {"line": 237, "column": 27}}, "237": {"start": {"line": 238, "column": 0}, "end": {"line": 238, "column": 42}}, "239": {"start": {"line": 240, "column": 0}, "end": {"line": 240, "column": 18}}, "240": {"start": {"line": 241, "column": 0}, "end": {"line": 241, "column": 61}}, "241": {"start": {"line": 242, "column": 0}, "end": {"line": 242, "column": 18}}, "242": {"start": {"line": 243, "column": 0}, "end": {"line": 243, "column": 5}}, "245": {"start": {"line": 246, "column": 0}, "end": {"line": 246, "column": 51}}, "246": {"start": {"line": 247, "column": 0}, "end": {"line": 247, "column": 61}}, "247": {"start": {"line": 248, "column": 0}, "end": {"line": 248, "column": 18}}, "248": {"start": {"line": 249, "column": 0}, "end": {"line": 249, "column": 5}}, "251": {"start": {"line": 252, "column": 0}, "end": {"line": 252, "column": 43}}, "252": {"start": {"line": 253, "column": 0}, "end": {"line": 253, "column": 19}}, "253": {"start": {"line": 254, "column": 0}, "end": {"line": 254, "column": 5}}, "256": {"start": {"line": 257, "column": 0}, "end": {"line": 257, "column": 19}}, "257": {"start": {"line": 258, "column": 0}, "end": {"line": 258, "column": 29}}, "258": {"start": {"line": 259, "column": 0}, "end": {"line": 259, "column": 16}}, "259": {"start": {"line": 260, "column": 0}, "end": {"line": 260, "column": 3}}, "261": {"start": {"line": 262, "column": 0}, "end": {"line": 262, "column": 28}}, "262": {"start": {"line": 263, "column": 0}, "end": {"line": 263, "column": 30}}, "263": {"start": {"line": 264, "column": 0}, "end": {"line": 264, "column": 3}}, "264": {"start": {"line": 265, "column": 0}, "end": {"line": 265, "column": 1}}}, "s": {"11": 1, "12": 1, "13": 1, "14": 1, "15": 1, "16": 1, "17": 1, "20": 1, "21": 43, "23": 43, "24": 43, "25": 43, "26": 43, "27": 43, "28": 43, "29": 43, "30": 43, "31": 43, "34": 1, "35": 23, "36": 1, "37": 1, "39": 22, "41": 23, "42": 1, "43": 1, "45": 23, "46": 3, "47": 3, "48": 3, "49": 3, "50": 3, "53": 22, "54": 8, "55": 8, "56": 8, "57": 8, "58": 8, "60": 10, "61": 10, "62": 10, "65": 1, "66": 14, "67": 0, "68": 0, "70": 14, "72": 14, "73": 2, "74": 2, "75": 2, "76": 2, "77": 2, "80": 13, "81": 5, "82": 5, "83": 5, "84": 5, "85": 5, "87": 7, "88": 7, "89": 7, "92": 1, "93": 13, "94": 1, "95": 1, "98": 12, "100": 13, "101": 7, "102": 7, "103": 7, "104": 7, "105": 7, "107": 7, "108": 3, "109": 3, "110": 3, "111": 3, "112": 3, "114": 2, "115": 2, "118": 1, "119": 9, "121": 9, "122": 1, "123": 1, "124": 1, "125": 1, "126": 1, "128": 8, "129": 8, "132": 1, "133": 8, "135": 8, "136": 1, "137": 1, "138": 1, "139": 1, "140": 1, "142": 7, "143": 7, "146": 1, "148": 36, "150": 9, "151": 9, "152": 5, "153": 5, "154": 9, "156": 36, "158": 36, "159": 3, "160": 3, "162": 36, "163": 1, "164": 1, "166": 36, "167": 1, "168": 1, "170": 26, "171": 26, "174": 1, "175": 35, "176": 1, "177": 1, "180": 34, "182": 35, "183": 14, "184": 14, "186": 20, "187": 20, "190": 1, "191": 0, "192": 0, "193": 0, "195": 0, "197": 0, "198": 0, "199": 0, "200": 0, "201": 0, "202": 0, "204": 0, "205": 0, "206": 0, "209": 1, "210": 12, "211": 12, "214": 12, "215": 1, "216": 1, "218": 10, "219": 12, "220": 1, "221": 1, "222": 12, "225": 1, "226": 1, "227": 7, "228": 7, "230": 1, "231": 7, "232": 7, "233": 7, "235": 1, "236": 22, "237": 22, "239": 22, "240": 7, "241": 7, "242": 7, "245": 22, "246": 1, "247": 1, "248": 1, "251": 22, "252": 4, "253": 4, "256": 10, "257": 10, "258": 10, "259": 22, "261": 1, "262": 1, "263": 1, "264": 1}, "branchMap": {"0": {"type": "branch", "line": 21, "loc": {"start": {"line": 21, "column": 7}, "end": {"line": 32, "column": 1}}, "locations": [{"start": {"line": 21, "column": 7}, "end": {"line": 32, "column": 1}}]}, "1": {"type": "branch", "line": 22, "loc": {"start": {"line": 22, "column": 33}, "end": {"line": 22, "column": 43}}, "locations": [{"start": {"line": 22, "column": 33}, "end": {"line": 22, "column": 43}}]}, "2": {"type": "branch", "line": 35, "loc": {"start": {"line": 35, "column": 7}, "end": {"line": 63, "column": 1}}, "locations": [{"start": {"line": 35, "column": 7}, "end": {"line": 63, "column": 1}}]}, "3": {"type": "branch", "line": 36, "loc": {"start": {"line": 36, "column": 32}, "end": {"line": 38, "column": 3}}, "locations": [{"start": {"line": 36, "column": 32}, "end": {"line": 38, "column": 3}}]}, "4": {"type": "branch", "line": 38, "loc": {"start": {"line": 38, "column": 2}, "end": {"line": 42, "column": 16}}, "locations": [{"start": {"line": 38, "column": 2}, "end": {"line": 42, "column": 16}}]}, "5": {"type": "branch", "line": 42, "loc": {"start": {"line": 42, "column": 16}, "end": {"line": 44, "column": 3}}, "locations": [{"start": {"line": 42, "column": 16}, "end": {"line": 44, "column": 3}}]}, "6": {"type": "branch", "line": 44, "loc": {"start": {"line": 44, "column": 2}, "end": {"line": 46, "column": 50}}, "locations": [{"start": {"line": 44, "column": 2}, "end": {"line": 46, "column": 50}}]}, "7": {"type": "branch", "line": 46, "loc": {"start": {"line": 46, "column": 50}, "end": {"line": 51, "column": 3}}, "locations": [{"start": {"line": 46, "column": 50}, "end": {"line": 51, "column": 3}}]}, "8": {"type": "branch", "line": 51, "loc": {"start": {"line": 51, "column": 2}, "end": {"line": 59, "column": 3}}, "locations": [{"start": {"line": 51, "column": 2}, "end": {"line": 59, "column": 3}}]}, "9": {"type": "branch", "line": 51, "loc": {"start": {"line": 51, "column": 2}, "end": {"line": 54, "column": 33}}, "locations": [{"start": {"line": 51, "column": 2}, "end": {"line": 54, "column": 33}}]}, "10": {"type": "branch", "line": 54, "loc": {"start": {"line": 54, "column": 33}, "end": {"line": 59, "column": 3}}, "locations": [{"start": {"line": 54, "column": 33}, "end": {"line": 59, "column": 3}}]}, "11": {"type": "branch", "line": 59, "loc": {"start": {"line": 59, "column": 2}, "end": {"line": 63, "column": 1}}, "locations": [{"start": {"line": 59, "column": 2}, "end": {"line": 63, "column": 1}}]}, "12": {"type": "branch", "line": 66, "loc": {"start": {"line": 66, "column": 7}, "end": {"line": 90, "column": 1}}, "locations": [{"start": {"line": 66, "column": 7}, "end": {"line": 90, "column": 1}}]}, "13": {"type": "branch", "line": 67, "loc": {"start": {"line": 67, "column": 39}, "end": {"line": 69, "column": 3}}, "locations": [{"start": {"line": 67, "column": 39}, "end": {"line": 69, "column": 3}}]}, "14": {"type": "branch", "line": 73, "loc": {"start": {"line": 73, "column": 57}, "end": {"line": 78, "column": 3}}, "locations": [{"start": {"line": 73, "column": 57}, "end": {"line": 78, "column": 3}}]}, "15": {"type": "branch", "line": 78, "loc": {"start": {"line": 78, "column": 2}, "end": {"line": 86, "column": 3}}, "locations": [{"start": {"line": 78, "column": 2}, "end": {"line": 86, "column": 3}}]}, "16": {"type": "branch", "line": 78, "loc": {"start": {"line": 78, "column": 2}, "end": {"line": 81, "column": 33}}, "locations": [{"start": {"line": 78, "column": 2}, "end": {"line": 81, "column": 33}}]}, "17": {"type": "branch", "line": 81, "loc": {"start": {"line": 81, "column": 33}, "end": {"line": 86, "column": 3}}, "locations": [{"start": {"line": 81, "column": 33}, "end": {"line": 86, "column": 3}}]}, "18": {"type": "branch", "line": 86, "loc": {"start": {"line": 86, "column": 2}, "end": {"line": 90, "column": 1}}, "locations": [{"start": {"line": 86, "column": 2}, "end": {"line": 90, "column": 1}}]}, "19": {"type": "branch", "line": 93, "loc": {"start": {"line": 93, "column": 7}, "end": {"line": 116, "column": 1}}, "locations": [{"start": {"line": 93, "column": 7}, "end": {"line": 116, "column": 1}}]}, "20": {"type": "branch", "line": 94, "loc": {"start": {"line": 94, "column": 31}, "end": {"line": 96, "column": 3}}, "locations": [{"start": {"line": 94, "column": 31}, "end": {"line": 96, "column": 3}}]}, "21": {"type": "branch", "line": 96, "loc": {"start": {"line": 96, "column": 2}, "end": {"line": 101, "column": 49}}, "locations": [{"start": {"line": 96, "column": 2}, "end": {"line": 101, "column": 49}}]}, "22": {"type": "branch", "line": 101, "loc": {"start": {"line": 101, "column": 49}, "end": {"line": 113, "column": 3}}, "locations": [{"start": {"line": 101, "column": 49}, "end": {"line": 113, "column": 3}}]}, "23": {"type": "branch", "line": 106, "loc": {"start": {"line": 106, "column": 2}, "end": {"line": 108, "column": 49}}, "locations": [{"start": {"line": 106, "column": 2}, "end": {"line": 108, "column": 49}}]}, "24": {"type": "branch", "line": 108, "loc": {"start": {"line": 108, "column": 49}, "end": {"line": 113, "column": 3}}, "locations": [{"start": {"line": 108, "column": 49}, "end": {"line": 113, "column": 3}}]}, "25": {"type": "branch", "line": 113, "loc": {"start": {"line": 113, "column": 2}, "end": {"line": 116, "column": 1}}, "locations": [{"start": {"line": 113, "column": 2}, "end": {"line": 116, "column": 1}}]}, "26": {"type": "branch", "line": 119, "loc": {"start": {"line": 119, "column": 7}, "end": {"line": 130, "column": 1}}, "locations": [{"start": {"line": 119, "column": 7}, "end": {"line": 130, "column": 1}}]}, "27": {"type": "branch", "line": 122, "loc": {"start": {"line": 122, "column": 43}, "end": {"line": 127, "column": 3}}, "locations": [{"start": {"line": 122, "column": 43}, "end": {"line": 127, "column": 3}}]}, "28": {"type": "branch", "line": 127, "loc": {"start": {"line": 127, "column": 2}, "end": {"line": 130, "column": 1}}, "locations": [{"start": {"line": 127, "column": 2}, "end": {"line": 130, "column": 1}}]}, "29": {"type": "branch", "line": 133, "loc": {"start": {"line": 133, "column": 7}, "end": {"line": 144, "column": 1}}, "locations": [{"start": {"line": 133, "column": 7}, "end": {"line": 144, "column": 1}}]}, "30": {"type": "branch", "line": 136, "loc": {"start": {"line": 136, "column": 40}, "end": {"line": 141, "column": 3}}, "locations": [{"start": {"line": 136, "column": 40}, "end": {"line": 141, "column": 3}}]}, "31": {"type": "branch", "line": 141, "loc": {"start": {"line": 141, "column": 2}, "end": {"line": 144, "column": 1}}, "locations": [{"start": {"line": 141, "column": 2}, "end": {"line": 144, "column": 1}}]}, "32": {"type": "branch", "line": 147, "loc": {"start": {"line": 147, "column": 7}, "end": {"line": 172, "column": 1}}, "locations": [{"start": {"line": 147, "column": 7}, "end": {"line": 172, "column": 1}}]}, "33": {"type": "branch", "line": 149, "loc": {"start": {"line": 149, "column": 33}, "end": {"line": 155, "column": 3}}, "locations": [{"start": {"line": 149, "column": 33}, "end": {"line": 155, "column": 3}}]}, "34": {"type": "branch", "line": 152, "loc": {"start": {"line": 152, "column": 84}, "end": {"line": 154, "column": 5}}, "locations": [{"start": {"line": 152, "column": 84}, "end": {"line": 154, "column": 5}}]}, "35": {"type": "branch", "line": 155, "loc": {"start": {"line": 155, "column": 2}, "end": {"line": 157, "column": 62}}, "locations": [{"start": {"line": 155, "column": 2}, "end": {"line": 157, "column": 62}}]}, "36": {"type": "branch", "line": 157, "loc": {"start": {"line": 157, "column": 31}, "end": {"line": 157, "column": 62}}, "locations": [{"start": {"line": 157, "column": 31}, "end": {"line": 157, "column": 62}}]}, "37": {"type": "branch", "line": 157, "loc": {"start": {"line": 157, "column": 58}, "end": {"line": 157, "column": 68}}, "locations": [{"start": {"line": 157, "column": 58}, "end": {"line": 157, "column": 68}}]}, "38": {"type": "branch", "line": 159, "loc": {"start": {"line": 159, "column": 15}, "end": {"line": 159, "column": 36}}, "locations": [{"start": {"line": 159, "column": 15}, "end": {"line": 159, "column": 36}}]}, "39": {"type": "branch", "line": 159, "loc": {"start": {"line": 159, "column": 36}, "end": {"line": 163, "column": 38}}, "locations": [{"start": {"line": 159, "column": 36}, "end": {"line": 163, "column": 38}}]}, "40": {"type": "branch", "line": 159, "loc": {"start": {"line": 159, "column": 36}, "end": {"line": 161, "column": 3}}, "locations": [{"start": {"line": 159, "column": 36}, "end": {"line": 161, "column": 3}}]}, "41": {"type": "branch", "line": 161, "loc": {"start": {"line": 161, "column": 2}, "end": {"line": 163, "column": 27}}, "locations": [{"start": {"line": 161, "column": 2}, "end": {"line": 163, "column": 27}}]}, "42": {"type": "branch", "line": 163, "loc": {"start": {"line": 163, "column": 14}, "end": {"line": 163, "column": 38}}, "locations": [{"start": {"line": 163, "column": 14}, "end": {"line": 163, "column": 38}}]}, "43": {"type": "branch", "line": 163, "loc": {"start": {"line": 163, "column": 38}, "end": {"line": 167, "column": 38}}, "locations": [{"start": {"line": 163, "column": 38}, "end": {"line": 167, "column": 38}}]}, "44": {"type": "branch", "line": 163, "loc": {"start": {"line": 163, "column": 38}, "end": {"line": 165, "column": 3}}, "locations": [{"start": {"line": 163, "column": 38}, "end": {"line": 165, "column": 3}}]}, "45": {"type": "branch", "line": 165, "loc": {"start": {"line": 165, "column": 2}, "end": {"line": 167, "column": 27}}, "locations": [{"start": {"line": 165, "column": 2}, "end": {"line": 167, "column": 27}}]}, "46": {"type": "branch", "line": 167, "loc": {"start": {"line": 167, "column": 14}, "end": {"line": 167, "column": 38}}, "locations": [{"start": {"line": 167, "column": 14}, "end": {"line": 167, "column": 38}}]}, "47": {"type": "branch", "line": 167, "loc": {"start": {"line": 167, "column": 38}, "end": {"line": 172, "column": 1}}, "locations": [{"start": {"line": 167, "column": 38}, "end": {"line": 172, "column": 1}}]}, "48": {"type": "branch", "line": 167, "loc": {"start": {"line": 167, "column": 38}, "end": {"line": 169, "column": 3}}, "locations": [{"start": {"line": 167, "column": 38}, "end": {"line": 169, "column": 3}}]}, "49": {"type": "branch", "line": 169, "loc": {"start": {"line": 169, "column": 2}, "end": {"line": 172, "column": 1}}, "locations": [{"start": {"line": 169, "column": 2}, "end": {"line": 172, "column": 1}}]}, "50": {"type": "branch", "line": 175, "loc": {"start": {"line": 175, "column": 7}, "end": {"line": 188, "column": 1}}, "locations": [{"start": {"line": 175, "column": 7}, "end": {"line": 188, "column": 1}}]}, "51": {"type": "branch", "line": 176, "loc": {"start": {"line": 176, "column": 30}, "end": {"line": 178, "column": 3}}, "locations": [{"start": {"line": 176, "column": 30}, "end": {"line": 178, "column": 3}}]}, "52": {"type": "branch", "line": 178, "loc": {"start": {"line": 178, "column": 2}, "end": {"line": 183, "column": 29}}, "locations": [{"start": {"line": 178, "column": 2}, "end": {"line": 183, "column": 29}}]}, "53": {"type": "branch", "line": 183, "loc": {"start": {"line": 183, "column": 29}, "end": {"line": 185, "column": 3}}, "locations": [{"start": {"line": 183, "column": 29}, "end": {"line": 185, "column": 3}}]}, "54": {"type": "branch", "line": 185, "loc": {"start": {"line": 185, "column": 2}, "end": {"line": 188, "column": 1}}, "locations": [{"start": {"line": 185, "column": 2}, "end": {"line": 188, "column": 1}}]}, "55": {"type": "branch", "line": 210, "loc": {"start": {"line": 210, "column": 7}, "end": {"line": 223, "column": 1}}, "locations": [{"start": {"line": 210, "column": 7}, "end": {"line": 223, "column": 1}}]}, "56": {"type": "branch", "line": 215, "loc": {"start": {"line": 215, "column": 35}, "end": {"line": 220, "column": 11}}, "locations": [{"start": {"line": 215, "column": 35}, "end": {"line": 220, "column": 11}}]}, "57": {"type": "branch", "line": 215, "loc": {"start": {"line": 215, "column": 35}, "end": {"line": 217, "column": 5}}, "locations": [{"start": {"line": 215, "column": 35}, "end": {"line": 217, "column": 5}}]}, "58": {"type": "branch", "line": 220, "loc": {"start": {"line": 220, "column": 2}, "end": {"line": 222, "column": 3}}, "locations": [{"start": {"line": 220, "column": 2}, "end": {"line": 222, "column": 3}}]}, "59": {"type": "branch", "line": 227, "loc": {"start": {"line": 227, "column": 10}, "end": {"line": 229, "column": 36}}, "locations": [{"start": {"line": 227, "column": 10}, "end": {"line": 229, "column": 36}}]}, "60": {"type": "branch", "line": 231, "loc": {"start": {"line": 231, "column": 2}, "end": {"line": 234, "column": 3}}, "locations": [{"start": {"line": 231, "column": 2}, "end": {"line": 234, "column": 3}}]}, "61": {"type": "branch", "line": 236, "loc": {"start": {"line": 236, "column": 2}, "end": {"line": 260, "column": 3}}, "locations": [{"start": {"line": 236, "column": 2}, "end": {"line": 260, "column": 3}}]}, "62": {"type": "branch", "line": 240, "loc": {"start": {"line": 240, "column": 17}, "end": {"line": 243, "column": 5}}, "locations": [{"start": {"line": 240, "column": 17}, "end": {"line": 243, "column": 5}}]}, "63": {"type": "branch", "line": 243, "loc": {"start": {"line": 243, "column": 4}, "end": {"line": 246, "column": 50}}, "locations": [{"start": {"line": 243, "column": 4}, "end": {"line": 246, "column": 50}}]}, "64": {"type": "branch", "line": 246, "loc": {"start": {"line": 246, "column": 50}, "end": {"line": 249, "column": 5}}, "locations": [{"start": {"line": 246, "column": 50}, "end": {"line": 249, "column": 5}}]}, "65": {"type": "branch", "line": 249, "loc": {"start": {"line": 249, "column": 4}, "end": {"line": 252, "column": 42}}, "locations": [{"start": {"line": 249, "column": 4}, "end": {"line": 252, "column": 42}}]}, "66": {"type": "branch", "line": 252, "loc": {"start": {"line": 252, "column": 42}, "end": {"line": 254, "column": 5}}, "locations": [{"start": {"line": 252, "column": 42}, "end": {"line": 254, "column": 5}}]}, "67": {"type": "branch", "line": 254, "loc": {"start": {"line": 254, "column": 4}, "end": {"line": 259, "column": 16}}, "locations": [{"start": {"line": 254, "column": 4}, "end": {"line": 259, "column": 16}}]}, "68": {"type": "branch", "line": 262, "loc": {"start": {"line": 262, "column": 2}, "end": {"line": 264, "column": 3}}, "locations": [{"start": {"line": 262, "column": 2}, "end": {"line": 264, "column": 3}}]}}, "b": {"0": [43], "1": [0], "2": [23], "3": [1], "4": [22], "5": [1], "6": [21], "7": [3], "8": [22], "9": [18], "10": [8], "11": [10], "12": [14], "13": [0], "14": [2], "15": [13], "16": [12], "17": [5], "18": [7], "19": [13], "20": [1], "21": [12], "22": [7], "23": [5], "24": [3], "25": [2], "26": [9], "27": [1], "28": [8], "29": [8], "30": [1], "31": [7], "32": [36], "33": [9], "34": [5], "35": [31], "36": [4], "37": [27], "38": [29], "39": [31], "40": [3], "41": [28], "42": [22], "43": [31], "44": [1], "45": [27], "46": [1], "47": [31], "48": [1], "49": [26], "50": [35], "51": [1], "52": [34], "53": [14], "54": [20], "55": [12], "56": [10], "57": [1], "58": [1], "59": [7], "60": [7], "61": [22], "62": [7], "63": [15], "64": [1], "65": [14], "66": [4], "67": [10], "68": [1]}, "fnMap": {"0": {"name": "sanitizeHtml", "decl": {"start": {"line": 21, "column": 7}, "end": {"line": 32, "column": 1}}, "loc": {"start": {"line": 21, "column": 7}, "end": {"line": 32, "column": 1}}, "line": 21}, "1": {"name": "validateProjectName", "decl": {"start": {"line": 35, "column": 7}, "end": {"line": 63, "column": 1}}, "loc": {"start": {"line": 35, "column": 7}, "end": {"line": 63, "column": 1}}, "line": 35}, "2": {"name": "validateProjectDescription", "decl": {"start": {"line": 66, "column": 7}, "end": {"line": 90, "column": 1}}, "loc": {"start": {"line": 66, "column": 7}, "end": {"line": 90, "column": 1}}, "line": 66}, "3": {"name": "validatePin", "decl": {"start": {"line": 93, "column": 7}, "end": {"line": 116, "column": 1}}, "loc": {"start": {"line": 93, "column": 7}, "end": {"line": 116, "column": 1}}, "line": 93}, "4": {"name": "validateCategory", "decl": {"start": {"line": 119, "column": 7}, "end": {"line": 130, "column": 1}}, "loc": {"start": {"line": 119, "column": 7}, "end": {"line": 130, "column": 1}}, "line": 119}, "5": {"name": "validateUnitSystem", "decl": {"start": {"line": 133, "column": 7}, "end": {"line": 144, "column": 1}}, "loc": {"start": {"line": 133, "column": 7}, "end": {"line": 144, "column": 1}}, "line": 133}, "6": {"name": "validateNumeric", "decl": {"start": {"line": 147, "column": 7}, "end": {"line": 172, "column": 1}}, "loc": {"start": {"line": 147, "column": 7}, "end": {"line": 172, "column": 1}}, "line": 147}, "7": {"name": "validateUlid", "decl": {"start": {"line": 175, "column": 7}, "end": {"line": 188, "column": 1}}, "loc": {"start": {"line": 175, "column": 7}, "end": {"line": 188, "column": 1}}, "line": 175}, "8": {"name": "validateGeneralText", "decl": {"start": {"line": 191, "column": 7}, "end": {"line": 207, "column": 1}}, "loc": {"start": {"line": 191, "column": 7}, "end": {"line": 207, "column": 1}}, "line": 191}, "9": {"name": "validateJsonData", "decl": {"start": {"line": 210, "column": 7}, "end": {"line": 223, "column": 1}}, "loc": {"start": {"line": 210, "column": 7}, "end": {"line": 223, "column": 1}}, "line": 210}, "10": {"name": "<instance_members_initializer>", "decl": {"start": {"line": 227, "column": 10}, "end": {"line": 229, "column": 36}}, "loc": {"start": {"line": 227, "column": 10}, "end": {"line": 229, "column": 36}}, "line": 227}, "11": {"name": "RateLimiter", "decl": {"start": {"line": 231, "column": 2}, "end": {"line": 234, "column": 3}}, "loc": {"start": {"line": 231, "column": 2}, "end": {"line": 234, "column": 3}}, "line": 231}, "12": {"name": "isAllowed", "decl": {"start": {"line": 236, "column": 2}, "end": {"line": 260, "column": 3}}, "loc": {"start": {"line": 236, "column": 2}, "end": {"line": 260, "column": 3}}, "line": 236}, "13": {"name": "reset", "decl": {"start": {"line": 262, "column": 2}, "end": {"line": 264, "column": 3}}, "loc": {"start": {"line": 262, "column": 2}, "end": {"line": 264, "column": 3}}, "line": 262}}, "f": {"0": 43, "1": 23, "2": 14, "3": 13, "4": 9, "5": 8, "6": 36, "7": 35, "8": 0, "9": 12, "10": 7, "11": 7, "12": 22, "13": 1}}, "/Users/<USER>/Documents/SizeWise_Offline_App/lib/licensing.ts": {"path": "/Users/<USER>/Documents/SizeWise_Offline_App/lib/licensing.ts", "all": false, "statementMap": {"5": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 28}}, "7": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 18}}, "8": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": 111}}, "9": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": 1}}, "10": {"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 90}}, "11": {"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 1}}, "13": {"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 41}}, "14": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 25}}, "15": {"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 23}}, "16": {"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 13}}, "17": {"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 25}}, "18": {"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 145}}, "19": {"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 19}}, "20": {"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 17}}, "21": {"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 3}}, "23": {"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 63}}, "24": {"start": {"line": 25, "column": 0}, "end": {"line": 25, "column": 28}}, "25": {"start": {"line": 26, "column": 0}, "end": {"line": 26, "column": 25}}, "26": {"start": {"line": 27, "column": 0}, "end": {"line": 27, "column": 37}}, "27": {"start": {"line": 28, "column": 0}, "end": {"line": 28, "column": 3}}, "28": {"start": {"line": 29, "column": 0}, "end": {"line": 29, "column": 24}}, "29": {"start": {"line": 30, "column": 0}, "end": {"line": 30, "column": 79}}, "30": {"start": {"line": 31, "column": 0}, "end": {"line": 31, "column": 15}}, "31": {"start": {"line": 32, "column": 0}, "end": {"line": 32, "column": 13}}, "32": {"start": {"line": 33, "column": 0}, "end": {"line": 33, "column": 1}}, "34": {"start": {"line": 35, "column": 0}, "end": {"line": 35, "column": 54}}, "35": {"start": {"line": 36, "column": 0}, "end": {"line": 36, "column": 23}}, "36": {"start": {"line": 37, "column": 0}, "end": {"line": 37, "column": 46}}, "37": {"start": {"line": 38, "column": 0}, "end": {"line": 38, "column": 1}}, "39": {"start": {"line": 40, "column": 0}, "end": {"line": 40, "column": 37}}, "40": {"start": {"line": 41, "column": 0}, "end": {"line": 41, "column": 23}}, "41": {"start": {"line": 42, "column": 0}, "end": {"line": 42, "column": 25}}, "42": {"start": {"line": 43, "column": 0}, "end": {"line": 43, "column": 103}}, "43": {"start": {"line": 44, "column": 0}, "end": {"line": 44, "column": 10}}, "44": {"start": {"line": 45, "column": 0}, "end": {"line": 45, "column": 13}}, "45": {"start": {"line": 46, "column": 0}, "end": {"line": 46, "column": 60}}, "46": {"start": {"line": 47, "column": 0}, "end": {"line": 47, "column": 4}}, "47": {"start": {"line": 48, "column": 0}, "end": {"line": 48, "column": 1}}, "49": {"start": {"line": 50, "column": 0}, "end": {"line": 50, "column": 41}}, "50": {"start": {"line": 51, "column": 0}, "end": {"line": 51, "column": 23}}, "51": {"start": {"line": 52, "column": 0}, "end": {"line": 52, "column": 37}}, "52": {"start": {"line": 53, "column": 0}, "end": {"line": 53, "column": 41}}, "54": {"start": {"line": 55, "column": 0}, "end": {"line": 55, "column": 10}}, "55": {"start": {"line": 56, "column": 0}, "end": {"line": 56, "column": 12}}, "56": {"start": {"line": 57, "column": 0}, "end": {"line": 57, "column": 17}}, "57": {"start": {"line": 58, "column": 0}, "end": {"line": 58, "column": 66}}, "58": {"start": {"line": 59, "column": 0}, "end": {"line": 59, "column": 4}}, "59": {"start": {"line": 60, "column": 0}, "end": {"line": 60, "column": 1}}, "61": {"start": {"line": 62, "column": 0}, "end": {"line": 62, "column": 71}}}, "s": {"5": 1, "7": 20, "8": 20, "9": 20, "10": 12, "11": 12, "13": 12, "14": 12, "15": 12, "16": 12, "17": 8, "18": 8, "19": 8, "20": 8, "21": 8, "23": 4, "24": 12, "25": 1, "26": 1, "27": 1, "28": 4, "29": 12, "30": 4, "31": 4, "32": 4, "34": 3, "35": 3, "36": 3, "37": 3, "39": 4, "40": 4, "41": 4, "42": 4, "43": 4, "44": 4, "45": 4, "46": 4, "47": 4, "49": 1, "50": 1, "51": 1, "52": 1, "54": 1, "55": 1, "56": 1, "57": 1, "58": 1, "59": 1, "61": 1}, "branchMap": {"0": {"type": "branch", "line": 8, "loc": {"start": {"line": 8, "column": 0}, "end": {"line": 10, "column": 1}}, "locations": [{"start": {"line": 8, "column": 0}, "end": {"line": 10, "column": 1}}]}, "1": {"type": "branch", "line": 9, "loc": {"start": {"line": 9, "column": 57}, "end": {"line": 9, "column": 81}}, "locations": [{"start": {"line": 9, "column": 57}, "end": {"line": 9, "column": 81}}]}, "2": {"type": "branch", "line": 9, "loc": {"start": {"line": 9, "column": 77}, "end": {"line": 9, "column": 87}}, "locations": [{"start": {"line": 9, "column": 77}, "end": {"line": 9, "column": 87}}]}, "3": {"type": "branch", "line": 9, "loc": {"start": {"line": 9, "column": 87}, "end": {"line": 9, "column": 111}}, "locations": [{"start": {"line": 9, "column": 87}, "end": {"line": 9, "column": 111}}]}, "4": {"type": "branch", "line": 11, "loc": {"start": {"line": 11, "column": 0}, "end": {"line": 12, "column": 1}}, "locations": [{"start": {"line": 11, "column": 0}, "end": {"line": 12, "column": 1}}]}, "5": {"type": "branch", "line": 11, "loc": {"start": {"line": 11, "column": 80}, "end": {"line": 11, "column": 90}}, "locations": [{"start": {"line": 11, "column": 80}, "end": {"line": 11, "column": 90}}]}, "6": {"type": "branch", "line": 14, "loc": {"start": {"line": 14, "column": 0}, "end": {"line": 33, "column": 1}}, "locations": [{"start": {"line": 14, "column": 0}, "end": {"line": 33, "column": 1}}]}, "7": {"type": "branch", "line": 17, "loc": {"start": {"line": 17, "column": 12}, "end": {"line": 22, "column": 3}}, "locations": [{"start": {"line": 17, "column": 12}, "end": {"line": 22, "column": 3}}]}, "8": {"type": "branch", "line": 22, "loc": {"start": {"line": 22, "column": 2}, "end": {"line": 25, "column": 27}}, "locations": [{"start": {"line": 22, "column": 2}, "end": {"line": 25, "column": 27}}]}, "9": {"type": "branch", "line": 25, "loc": {"start": {"line": 25, "column": 27}, "end": {"line": 28, "column": 3}}, "locations": [{"start": {"line": 25, "column": 27}, "end": {"line": 28, "column": 3}}]}, "10": {"type": "branch", "line": 28, "loc": {"start": {"line": 28, "column": 2}, "end": {"line": 30, "column": 33}}, "locations": [{"start": {"line": 28, "column": 2}, "end": {"line": 30, "column": 33}}]}, "11": {"type": "branch", "line": 30, "loc": {"start": {"line": 30, "column": 22}, "end": {"line": 30, "column": 58}}, "locations": [{"start": {"line": 30, "column": 22}, "end": {"line": 30, "column": 58}}]}, "12": {"type": "branch", "line": 30, "loc": {"start": {"line": 30, "column": 58}, "end": {"line": 30, "column": 79}}, "locations": [{"start": {"line": 30, "column": 58}, "end": {"line": 30, "column": 79}}]}, "13": {"type": "branch", "line": 30, "loc": {"start": {"line": 30, "column": 72}, "end": {"line": 33, "column": 1}}, "locations": [{"start": {"line": 30, "column": 72}, "end": {"line": 33, "column": 1}}]}, "14": {"type": "branch", "line": 35, "loc": {"start": {"line": 35, "column": 0}, "end": {"line": 38, "column": 1}}, "locations": [{"start": {"line": 35, "column": 0}, "end": {"line": 38, "column": 1}}]}, "15": {"type": "branch", "line": 37, "loc": {"start": {"line": 37, "column": 15}, "end": {"line": 37, "column": 46}}, "locations": [{"start": {"line": 37, "column": 15}, "end": {"line": 37, "column": 46}}]}, "16": {"type": "branch", "line": 40, "loc": {"start": {"line": 40, "column": 0}, "end": {"line": 48, "column": 1}}, "locations": [{"start": {"line": 40, "column": 0}, "end": {"line": 48, "column": 1}}]}, "17": {"type": "branch", "line": 43, "loc": {"start": {"line": 43, "column": 97}, "end": {"line": 43, "column": 103}}, "locations": [{"start": {"line": 43, "column": 97}, "end": {"line": 43, "column": 103}}]}, "18": {"type": "branch", "line": 46, "loc": {"start": {"line": 46, "column": 30}, "end": {"line": 46, "column": 60}}, "locations": [{"start": {"line": 46, "column": 30}, "end": {"line": 46, "column": 60}}]}, "19": {"type": "branch", "line": 50, "loc": {"start": {"line": 50, "column": 0}, "end": {"line": 60, "column": 1}}, "locations": [{"start": {"line": 50, "column": 0}, "end": {"line": 60, "column": 1}}]}, "20": {"type": "branch", "line": 58, "loc": {"start": {"line": 58, "column": 58}, "end": {"line": 58, "column": 66}}, "locations": [{"start": {"line": 58, "column": 58}, "end": {"line": 58, "column": 66}}]}}, "b": {"0": [20], "1": [12], "2": [6], "3": [2], "4": [12], "5": [0], "6": [12], "7": [8], "8": [4], "9": [1], "10": [4], "11": [3], "12": [2], "13": [4], "14": [3], "15": [0], "16": [4], "17": [0], "18": [3], "19": [1], "20": [0]}, "fnMap": {"0": {"name": "readLS", "decl": {"start": {"line": 8, "column": 0}, "end": {"line": 10, "column": 1}}, "loc": {"start": {"line": 8, "column": 0}, "end": {"line": 10, "column": 1}}, "line": 8}, "1": {"name": "writeLS", "decl": {"start": {"line": 11, "column": 0}, "end": {"line": 12, "column": 1}}, "loc": {"start": {"line": 11, "column": 0}, "end": {"line": 12, "column": 1}}, "line": 11}, "2": {"name": "bootstrapLicense", "decl": {"start": {"line": 14, "column": 0}, "end": {"line": 33, "column": 1}}, "loc": {"start": {"line": 14, "column": 0}, "end": {"line": 33, "column": 1}}, "line": 14}, "3": {"name": "getEdition", "decl": {"start": {"line": 35, "column": 0}, "end": {"line": 38, "column": 1}}, "loc": {"start": {"line": 35, "column": 0}, "end": {"line": 38, "column": 1}}, "line": 35}, "4": {"name": "getTrialInfo", "decl": {"start": {"line": 40, "column": 0}, "end": {"line": 48, "column": 1}}, "loc": {"start": {"line": 40, "column": 0}, "end": {"line": 48, "column": 1}}, "line": 40}, "5": {"name": "getLicenseStatus", "decl": {"start": {"line": 50, "column": 0}, "end": {"line": 60, "column": 1}}, "loc": {"start": {"line": 50, "column": 0}, "end": {"line": 60, "column": 1}}, "line": 50}}, "f": {"0": 20, "1": 12, "2": 12, "3": 3, "4": 4, "5": 1}}, "/Users/<USER>/Documents/SizeWise_Offline_App/lib/security.ts": {"path": "/Users/<USER>/Documents/SizeWise_Offline_App/lib/security.ts", "all": true, "statementMap": {"5": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 27}}, "6": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 28}}, "7": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 82}}, "8": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": 91}}, "9": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": 42}}, "10": {"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 25}}, "11": {"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 28}}, "12": {"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 26}}, "13": {"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 27}}, "14": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 26}}, "15": {"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 27}}, "16": {"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 26}}, "17": {"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 25}}, "18": {"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 28}}, "19": {"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 32}}, "20": {"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 33}}, "21": {"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 2}}, "24": {"start": {"line": 25, "column": 0}, "end": {"line": 25, "column": 45}}, "25": {"start": {"line": 26, "column": 0}, "end": {"line": 26, "column": 35}}, "26": {"start": {"line": 27, "column": 0}, "end": {"line": 27, "column": 71}}, "27": {"start": {"line": 28, "column": 0}, "end": {"line": 28, "column": 16}}, "28": {"start": {"line": 29, "column": 0}, "end": {"line": 29, "column": 1}}, "31": {"start": {"line": 32, "column": 0}, "end": {"line": 32, "column": 33}}, "32": {"start": {"line": 33, "column": 0}, "end": {"line": 33, "column": 49}}, "33": {"start": {"line": 34, "column": 0}, "end": {"line": 34, "column": 38}}, "34": {"start": {"line": 35, "column": 0}, "end": {"line": 35, "column": 28}}, "35": {"start": {"line": 36, "column": 0}, "end": {"line": 36, "column": 38}}, "36": {"start": {"line": 37, "column": 0}, "end": {"line": 37, "column": 55}}, "37": {"start": {"line": 38, "column": 0}, "end": {"line": 38, "column": 79}}, "38": {"start": {"line": 39, "column": 0}, "end": {"line": 39, "column": 68}}, "39": {"start": {"line": 40, "column": 0}, "end": {"line": 40, "column": 2}}, "42": {"start": {"line": 43, "column": 0}, "end": {"line": 43, "column": 76}}, "43": {"start": {"line": 44, "column": 0}, "end": {"line": 44, "column": 7}}, "44": {"start": {"line": 45, "column": 0}, "end": {"line": 45, "column": 35}}, "47": {"start": {"line": 48, "column": 0}, "end": {"line": 48, "column": 41}}, "48": {"start": {"line": 49, "column": 0}, "end": {"line": 49, "column": 18}}, "49": {"start": {"line": 50, "column": 0}, "end": {"line": 50, "column": 5}}, "52": {"start": {"line": 53, "column": 0}, "end": {"line": 53, "column": 40}}, "53": {"start": {"line": 54, "column": 0}, "end": {"line": 54, "column": 18}}, "54": {"start": {"line": 55, "column": 0}, "end": {"line": 55, "column": 5}}, "57": {"start": {"line": 58, "column": 0}, "end": {"line": 58, "column": 45}}, "58": {"start": {"line": 59, "column": 0}, "end": {"line": 59, "column": 18}}, "59": {"start": {"line": 60, "column": 0}, "end": {"line": 60, "column": 5}}, "62": {"start": {"line": 63, "column": 0}, "end": {"line": 63, "column": 83}}, "63": {"start": {"line": 64, "column": 0}, "end": {"line": 64, "column": 18}}, "64": {"start": {"line": 65, "column": 0}, "end": {"line": 65, "column": 5}}, "67": {"start": {"line": 68, "column": 0}, "end": {"line": 68, "column": 17}}, "68": {"start": {"line": 69, "column": 0}, "end": {"line": 69, "column": 19}}, "70": {"start": {"line": 71, "column": 0}, "end": {"line": 71, "column": 17}}, "71": {"start": {"line": 72, "column": 0}, "end": {"line": 72, "column": 3}}, "72": {"start": {"line": 73, "column": 0}, "end": {"line": 73, "column": 1}}, "75": {"start": {"line": 76, "column": 0}, "end": {"line": 76, "column": 61}}, "76": {"start": {"line": 77, "column": 0}, "end": {"line": 77, "column": 49}}, "79": {"start": {"line": 80, "column": 0}, "end": {"line": 80, "column": 50}}, "82": {"start": {"line": 83, "column": 0}, "end": {"line": 83, "column": 66}}, "83": {"start": {"line": 84, "column": 0}, "end": {"line": 84, "column": 17}}, "84": {"start": {"line": 85, "column": 0}, "end": {"line": 85, "column": 3}}, "87": {"start": {"line": 88, "column": 0}, "end": {"line": 88, "column": 68}}, "88": {"start": {"line": 89, "column": 0}, "end": {"line": 89, "column": 17}}, "89": {"start": {"line": 90, "column": 0}, "end": {"line": 90, "column": 3}}, "92": {"start": {"line": 93, "column": 0}, "end": {"line": 93, "column": 34}}, "93": {"start": {"line": 94, "column": 0}, "end": {"line": 94, "column": 17}}, "94": {"start": {"line": 95, "column": 0}, "end": {"line": 95, "column": 3}}, "96": {"start": {"line": 97, "column": 0}, "end": {"line": 97, "column": 14}}, "97": {"start": {"line": 98, "column": 0}, "end": {"line": 98, "column": 1}}, "100": {"start": {"line": 101, "column": 0}, "end": {"line": 101, "column": 66}}, "101": {"start": {"line": 102, "column": 0}, "end": {"line": 102, "column": 39}}, "102": {"start": {"line": 103, "column": 0}, "end": {"line": 103, "column": 32}}, "103": {"start": {"line": 104, "column": 0}, "end": {"line": 104, "column": 80}}, "104": {"start": {"line": 105, "column": 0}, "end": {"line": 105, "column": 1}}, "107": {"start": {"line": 108, "column": 0}, "end": {"line": 108, "column": 31}}, "108": {"start": {"line": 109, "column": 0}, "end": {"line": 109, "column": 43}}, "109": {"start": {"line": 110, "column": 0}, "end": {"line": 110, "column": 50}}, "110": {"start": {"line": 111, "column": 0}, "end": {"line": 111, "column": 29}}, "111": {"start": {"line": 112, "column": 0}, "end": {"line": 112, "column": 2}}, "114": {"start": {"line": 115, "column": 0}, "end": {"line": 115, "column": 34}}, "115": {"start": {"line": 116, "column": 0}, "end": {"line": 116, "column": 17}}, "116": {"start": {"line": 117, "column": 0}, "end": {"line": 117, "column": 20}}, "117": {"start": {"line": 118, "column": 0}, "end": {"line": 118, "column": 44}}, "118": {"start": {"line": 119, "column": 0}, "end": {"line": 119, "column": 62}}, "119": {"start": {"line": 120, "column": 0}, "end": {"line": 120, "column": 4}}, "120": {"start": {"line": 121, "column": 0}, "end": {"line": 121, "column": 17}}, "121": {"start": {"line": 122, "column": 0}, "end": {"line": 122, "column": 22}}, "122": {"start": {"line": 123, "column": 0}, "end": {"line": 123, "column": 36}}, "123": {"start": {"line": 124, "column": 0}, "end": {"line": 124, "column": 3}}, "124": {"start": {"line": 125, "column": 0}, "end": {"line": 125, "column": 2}}, "127": {"start": {"line": 128, "column": 0}, "end": {"line": 128, "column": 65}}, "128": {"start": {"line": 129, "column": 0}, "end": {"line": 129, "column": 53}}, "129": {"start": {"line": 130, "column": 0}, "end": {"line": 130, "column": 41}}, "130": {"start": {"line": 131, "column": 0}, "end": {"line": 131, "column": 1}}, "133": {"start": {"line": 134, "column": 0}, "end": {"line": 134, "column": 74}}, "134": {"start": {"line": 135, "column": 0}, "end": {"line": 135, "column": 45}}, "135": {"start": {"line": 136, "column": 0}, "end": {"line": 136, "column": 20}}, "136": {"start": {"line": 137, "column": 0}, "end": {"line": 137, "column": 14}}, "137": {"start": {"line": 138, "column": 0}, "end": {"line": 138, "column": 10}}, "138": {"start": {"line": 139, "column": 0}, "end": {"line": 139, "column": 12}}, "139": {"start": {"line": 140, "column": 0}, "end": {"line": 140, "column": 81}}, "140": {"start": {"line": 141, "column": 0}, "end": {"line": 141, "column": 4}}, "142": {"start": {"line": 143, "column": 0}, "end": {"line": 143, "column": 39}}, "146": {"start": {"line": 147, "column": 0}, "end": {"line": 147, "column": 1}}, "149": {"start": {"line": 150, "column": 0}, "end": {"line": 150, "column": 61}}, "150": {"start": {"line": 151, "column": 0}, "end": {"line": 151, "column": 49}}, "153": {"start": {"line": 154, "column": 0}, "end": {"line": 154, "column": 46}}, "154": {"start": {"line": 155, "column": 0}, "end": {"line": 155, "column": 38}}, "155": {"start": {"line": 156, "column": 0}, "end": {"line": 156, "column": 17}}, "156": {"start": {"line": 157, "column": 0}, "end": {"line": 157, "column": 3}}, "159": {"start": {"line": 160, "column": 0}, "end": {"line": 160, "column": 65}}, "160": {"start": {"line": 161, "column": 0}, "end": {"line": 161, "column": 37}}, "161": {"start": {"line": 162, "column": 0}, "end": {"line": 162, "column": 17}}, "162": {"start": {"line": 163, "column": 0}, "end": {"line": 163, "column": 3}}, "165": {"start": {"line": 166, "column": 0}, "end": {"line": 166, "column": 30}}, "166": {"start": {"line": 167, "column": 0}, "end": {"line": 167, "column": 17}}, "167": {"start": {"line": 168, "column": 0}, "end": {"line": 168, "column": 3}}, "169": {"start": {"line": 170, "column": 0}, "end": {"line": 170, "column": 14}}, "170": {"start": {"line": 171, "column": 0}, "end": {"line": 171, "column": 1}}}, "s": {"5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "42": 0, "43": 0, "44": 0, "47": 0, "48": 0, "49": 0, "52": 0, "53": 0, "54": 0, "57": 0, "58": 0, "59": 0, "62": 0, "63": 0, "64": 0, "67": 0, "68": 0, "70": 0, "71": 0, "72": 0, "75": 0, "76": 0, "79": 0, "82": 0, "83": 0, "84": 0, "87": 0, "88": 0, "89": 0, "92": 0, "93": 0, "94": 0, "96": 0, "97": 0, "100": 0, "101": 0, "102": 0, "103": 0, "104": 0, "107": 0, "108": 0, "109": 0, "110": 0, "111": 0, "114": 0, "115": 0, "116": 0, "117": 0, "118": 0, "119": 0, "120": 0, "121": 0, "122": 0, "123": 0, "124": 0, "127": 0, "128": 0, "129": 0, "130": 0, "133": 0, "134": 0, "135": 0, "136": 0, "137": 0, "138": 0, "139": 0, "140": 0, "142": 0, "146": 0, "149": 0, "150": 0, "153": 0, "154": 0, "155": 0, "156": 0, "159": 0, "160": 0, "161": 0, "162": 0, "165": 0, "166": 0, "167": 0, "169": 0, "170": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 4764}, "end": {"line": 171, "column": 1}}, "locations": [{"start": {"line": 1, "column": 4764}, "end": {"line": 171, "column": 1}}]}}, "b": {"0": [1]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 4764}, "end": {"line": 171, "column": 1}}, "loc": {"start": {"line": 1, "column": 4764}, "end": {"line": 171, "column": 1}}, "line": 1}}, "f": {"0": 1}}, "/Users/<USER>/Documents/SizeWise_Offline_App/lib/sw-client.ts": {"path": "/Users/<USER>/Documents/SizeWise_Offline_App/lib/sw-client.ts", "all": true, "statementMap": {"2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 86}}}, "s": {"2": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 169}, "end": {"line": 3, "column": 86}}, "locations": [{"start": {"line": 1, "column": 169}, "end": {"line": 3, "column": 86}}]}}, "b": {"0": [1]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 169}, "end": {"line": 3, "column": 86}}, "loc": {"start": {"line": 1, "column": 169}, "end": {"line": 3, "column": 86}}, "line": 1}}, "f": {"0": 1}}, "/Users/<USER>/Documents/SizeWise_Offline_App/lib/units.ts": {"path": "/Users/<USER>/Documents/SizeWise_Offline_App/lib/units.ts", "all": true, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 63}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 75}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 87}}}, "s": {"0": 0, "1": 0, "2": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 227}, "end": {"line": 3, "column": 87}}, "locations": [{"start": {"line": 1, "column": 227}, "end": {"line": 3, "column": 87}}]}}, "b": {"0": [0]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 227}, "end": {"line": 3, "column": 87}}, "loc": {"start": {"line": 1, "column": 227}, "end": {"line": 3, "column": 87}}, "line": 1}}, "f": {"0": 0}}, "/Users/<USER>/Documents/SizeWise_Offline_App/lib/vault.ts": {"path": "/Users/<USER>/Documents/SizeWise_Offline_App/lib/vault.ts", "all": false, "statementMap": {"1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 82}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 67}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 46}}, "9": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": 39}}, "10": {"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 27}}, "11": {"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 37}}, "12": {"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 50}}, "29": {"start": {"line": 30, "column": 0}, "end": {"line": 30, "column": 32}}, "30": {"start": {"line": 31, "column": 0}, "end": {"line": 31, "column": 53}}, "31": {"start": {"line": 32, "column": 0}, "end": {"line": 32, "column": 19}}, "32": {"start": {"line": 33, "column": 0}, "end": {"line": 33, "column": 29}}, "33": {"start": {"line": 34, "column": 0}, "end": {"line": 34, "column": 3}}, "34": {"start": {"line": 35, "column": 0}, "end": {"line": 35, "column": 1}}, "37": {"start": {"line": 38, "column": 0}, "end": {"line": 38, "column": 46}}, "38": {"start": {"line": 39, "column": 0}, "end": {"line": 39, "column": 43}}, "39": {"start": {"line": 40, "column": 0}, "end": {"line": 40, "column": 68}}, "41": {"start": {"line": 42, "column": 0}, "end": {"line": 42, "column": 101}}, "42": {"start": {"line": 43, "column": 0}, "end": {"line": 43, "column": 54}}, "44": {"start": {"line": 45, "column": 0}, "end": {"line": 45, "column": 42}}, "45": {"start": {"line": 46, "column": 0}, "end": {"line": 46, "column": 59}}, "48": {"start": {"line": 49, "column": 0}, "end": {"line": 49, "column": 58}}, "49": {"start": {"line": 50, "column": 0}, "end": {"line": 50, "column": 81}}, "50": {"start": {"line": 51, "column": 0}, "end": {"line": 51, "column": 57}}, "51": {"start": {"line": 52, "column": 0}, "end": {"line": 52, "column": 7}}, "54": {"start": {"line": 55, "column": 0}, "end": {"line": 55, "column": 63}}, "55": {"start": {"line": 56, "column": 0}, "end": {"line": 56, "column": 91}}, "56": {"start": {"line": 57, "column": 0}, "end": {"line": 57, "column": 62}}, "57": {"start": {"line": 58, "column": 0}, "end": {"line": 58, "column": 62}}, "58": {"start": {"line": 59, "column": 0}, "end": {"line": 59, "column": 68}}, "59": {"start": {"line": 60, "column": 0}, "end": {"line": 60, "column": 7}}, "60": {"start": {"line": 61, "column": 0}, "end": {"line": 61, "column": 6}}, "61": {"start": {"line": 62, "column": 0}, "end": {"line": 62, "column": 5}}, "62": {"start": {"line": 63, "column": 0}, "end": {"line": 63, "column": 1}}, "65": {"start": {"line": 66, "column": 0}, "end": {"line": 66, "column": 56}}, "66": {"start": {"line": 67, "column": 0}, "end": {"line": 67, "column": 7}}, "67": {"start": {"line": 68, "column": 0}, "end": {"line": 68, "column": 43}}, "68": {"start": {"line": 69, "column": 0}, "end": {"line": 69, "column": 7}}, "69": {"start": {"line": 70, "column": 0}, "end": {"line": 70, "column": 24}}, "70": {"start": {"line": 71, "column": 0}, "end": {"line": 71, "column": 20}}, "71": {"start": {"line": 72, "column": 0}, "end": {"line": 72, "column": 8}}, "72": {"start": {"line": 73, "column": 0}, "end": {"line": 73, "column": 44}}, "73": {"start": {"line": 74, "column": 0}, "end": {"line": 74, "column": 28}}, "74": {"start": {"line": 75, "column": 0}, "end": {"line": 75, "column": 6}}, "75": {"start": {"line": 76, "column": 0}, "end": {"line": 76, "column": 19}}, "76": {"start": {"line": 77, "column": 0}, "end": {"line": 77, "column": 82}}, "77": {"start": {"line": 78, "column": 0}, "end": {"line": 78, "column": 3}}, "78": {"start": {"line": 79, "column": 0}, "end": {"line": 79, "column": 1}}, "81": {"start": {"line": 82, "column": 0}, "end": {"line": 82, "column": 51}}, "82": {"start": {"line": 83, "column": 0}, "end": {"line": 83, "column": 36}}, "83": {"start": {"line": 84, "column": 0}, "end": {"line": 84, "column": 80}}, "84": {"start": {"line": 85, "column": 0}, "end": {"line": 85, "column": 3}}, "86": {"start": {"line": 87, "column": 0}, "end": {"line": 87, "column": 7}}, "87": {"start": {"line": 88, "column": 0}, "end": {"line": 88, "column": 35}}, "88": {"start": {"line": 89, "column": 0}, "end": {"line": 89, "column": 69}}, "89": {"start": {"line": 90, "column": 0}, "end": {"line": 90, "column": 58}}, "91": {"start": {"line": 92, "column": 0}, "end": {"line": 92, "column": 45}}, "92": {"start": {"line": 93, "column": 0}, "end": {"line": 93, "column": 46}}, "94": {"start": {"line": 95, "column": 0}, "end": {"line": 95, "column": 39}}, "95": {"start": {"line": 96, "column": 0}, "end": {"line": 96, "column": 29}}, "96": {"start": {"line": 97, "column": 0}, "end": {"line": 97, "column": 38}}, "97": {"start": {"line": 98, "column": 0}, "end": {"line": 98, "column": 16}}, "99": {"start": {"line": 100, "column": 0}, "end": {"line": 100, "column": 15}}, "100": {"start": {"line": 101, "column": 0}, "end": {"line": 101, "column": 53}}, "101": {"start": {"line": 102, "column": 0}, "end": {"line": 102, "column": 41}}, "102": {"start": {"line": 103, "column": 0}, "end": {"line": 103, "column": 28}}, "103": {"start": {"line": 104, "column": 0}, "end": {"line": 104, "column": 27}}, "104": {"start": {"line": 105, "column": 0}, "end": {"line": 105, "column": 26}}, "105": {"start": {"line": 106, "column": 0}, "end": {"line": 106, "column": 11}}, "106": {"start": {"line": 107, "column": 0}, "end": {"line": 107, "column": 9}}, "107": {"start": {"line": 108, "column": 0}, "end": {"line": 108, "column": 8}}, "109": {"start": {"line": 110, "column": 0}, "end": {"line": 110, "column": 109}}, "110": {"start": {"line": 111, "column": 0}, "end": {"line": 111, "column": 7}}, "111": {"start": {"line": 112, "column": 0}, "end": {"line": 112, "column": 19}}, "112": {"start": {"line": 113, "column": 0}, "end": {"line": 113, "column": 79}}, "113": {"start": {"line": 114, "column": 0}, "end": {"line": 114, "column": 3}}, "114": {"start": {"line": 115, "column": 0}, "end": {"line": 115, "column": 1}}, "117": {"start": {"line": 118, "column": 0}, "end": {"line": 118, "column": 62}}, "118": {"start": {"line": 119, "column": 0}, "end": {"line": 119, "column": 33}}, "119": {"start": {"line": 120, "column": 0}, "end": {"line": 120, "column": 68}}, "120": {"start": {"line": 121, "column": 0}, "end": {"line": 121, "column": 56}}, "122": {"start": {"line": 123, "column": 0}, "end": {"line": 123, "column": 32}}, "123": {"start": {"line": 124, "column": 0}, "end": {"line": 124, "column": 21}}, "124": {"start": {"line": 125, "column": 0}, "end": {"line": 125, "column": 8}}, "125": {"start": {"line": 126, "column": 0}, "end": {"line": 126, "column": 26}}, "126": {"start": {"line": 127, "column": 0}, "end": {"line": 127, "column": 4}}, "128": {"start": {"line": 129, "column": 0}, "end": {"line": 129, "column": 43}}, "129": {"start": {"line": 130, "column": 0}, "end": {"line": 130, "column": 41}}, "130": {"start": {"line": 131, "column": 0}, "end": {"line": 131, "column": 40}}, "131": {"start": {"line": 132, "column": 0}, "end": {"line": 132, "column": 102}}, "132": {"start": {"line": 133, "column": 0}, "end": {"line": 133, "column": 5}}, "133": {"start": {"line": 134, "column": 0}, "end": {"line": 134, "column": 1}}, "136": {"start": {"line": 137, "column": 0}, "end": {"line": 137, "column": 96}}, "138": {"start": {"line": 139, "column": 0}, "end": {"line": 139, "column": 33}}, "139": {"start": {"line": 140, "column": 0}, "end": {"line": 140, "column": 84}}, "140": {"start": {"line": 141, "column": 0}, "end": {"line": 141, "column": 73}}, "141": {"start": {"line": 142, "column": 0}, "end": {"line": 142, "column": 3}}, "142": {"start": {"line": 143, "column": 0}, "end": {"line": 143, "column": 35}}, "143": {"start": {"line": 144, "column": 0}, "end": {"line": 144, "column": 36}}, "144": {"start": {"line": 145, "column": 0}, "end": {"line": 145, "column": 42}}, "147": {"start": {"line": 148, "column": 0}, "end": {"line": 148, "column": 56}}, "149": {"start": {"line": 150, "column": 0}, "end": {"line": 150, "column": 7}}, "150": {"start": {"line": 151, "column": 0}, "end": {"line": 151, "column": 50}}, "151": {"start": {"line": 152, "column": 0}, "end": {"line": 152, "column": 7}}, "152": {"start": {"line": 153, "column": 0}, "end": {"line": 153, "column": 24}}, "153": {"start": {"line": 154, "column": 0}, "end": {"line": 154, "column": 15}}, "154": {"start": {"line": 155, "column": 0}, "end": {"line": 155, "column": 8}}, "155": {"start": {"line": 156, "column": 0}, "end": {"line": 156, "column": 10}}, "156": {"start": {"line": 157, "column": 0}, "end": {"line": 157, "column": 16}}, "157": {"start": {"line": 158, "column": 0}, "end": {"line": 158, "column": 6}}, "159": {"start": {"line": 160, "column": 0}, "end": {"line": 160, "column": 40}}, "160": {"start": {"line": 161, "column": 0}, "end": {"line": 161, "column": 19}}, "161": {"start": {"line": 162, "column": 0}, "end": {"line": 162, "column": 71}}, "162": {"start": {"line": 163, "column": 0}, "end": {"line": 163, "column": 3}}, "163": {"start": {"line": 164, "column": 0}, "end": {"line": 164, "column": 1}}, "166": {"start": {"line": 167, "column": 0}, "end": {"line": 167, "column": 86}}, "168": {"start": {"line": 169, "column": 0}, "end": {"line": 169, "column": 33}}, "169": {"start": {"line": 170, "column": 0}, "end": {"line": 170, "column": 84}}, "170": {"start": {"line": 171, "column": 0}, "end": {"line": 171, "column": 73}}, "171": {"start": {"line": 172, "column": 0}, "end": {"line": 172, "column": 3}}, "172": {"start": {"line": 173, "column": 0}, "end": {"line": 173, "column": 35}}, "174": {"start": {"line": 175, "column": 0}, "end": {"line": 175, "column": 7}}, "175": {"start": {"line": 176, "column": 0}, "end": {"line": 176, "column": 50}}, "176": {"start": {"line": 177, "column": 0}, "end": {"line": 177, "column": 7}}, "177": {"start": {"line": 178, "column": 0}, "end": {"line": 178, "column": 24}}, "178": {"start": {"line": 179, "column": 0}, "end": {"line": 179, "column": 31}}, "179": {"start": {"line": 180, "column": 0}, "end": {"line": 180, "column": 8}}, "180": {"start": {"line": 181, "column": 0}, "end": {"line": 181, "column": 10}}, "181": {"start": {"line": 182, "column": 0}, "end": {"line": 182, "column": 15}}, "182": {"start": {"line": 183, "column": 0}, "end": {"line": 183, "column": 6}}, "184": {"start": {"line": 185, "column": 0}, "end": {"line": 185, "column": 38}}, "185": {"start": {"line": 186, "column": 0}, "end": {"line": 186, "column": 37}}, "186": {"start": {"line": 187, "column": 0}, "end": {"line": 187, "column": 19}}, "187": {"start": {"line": 188, "column": 0}, "end": {"line": 188, "column": 71}}, "188": {"start": {"line": 189, "column": 0}, "end": {"line": 189, "column": 3}}, "189": {"start": {"line": 190, "column": 0}, "end": {"line": 190, "column": 1}}, "192": {"start": {"line": 193, "column": 0}, "end": {"line": 193, "column": 26}}, "194": {"start": {"line": 195, "column": 0}, "end": {"line": 195, "column": 30}}, "195": {"start": {"line": 196, "column": 0}, "end": {"line": 196, "column": 22}}, "196": {"start": {"line": 197, "column": 0}, "end": {"line": 197, "column": 14}}, "197": {"start": {"line": 198, "column": 0}, "end": {"line": 198, "column": 59}}, "198": {"start": {"line": 199, "column": 0}, "end": {"line": 199, "column": 22}}, "199": {"start": {"line": 200, "column": 0}, "end": {"line": 200, "column": 38}}, "200": {"start": {"line": 201, "column": 0}, "end": {"line": 201, "column": 47}}, "201": {"start": {"line": 202, "column": 0}, "end": {"line": 202, "column": 5}}, "204": {"start": {"line": 205, "column": 0}, "end": {"line": 205, "column": 56}}, "205": {"start": {"line": 206, "column": 0}, "end": {"line": 206, "column": 39}}, "206": {"start": {"line": 207, "column": 0}, "end": {"line": 207, "column": 100}}, "207": {"start": {"line": 208, "column": 0}, "end": {"line": 208, "column": 101}}, "208": {"start": {"line": 209, "column": 0}, "end": {"line": 209, "column": 5}}, "210": {"start": {"line": 211, "column": 0}, "end": {"line": 211, "column": 50}}, "211": {"start": {"line": 212, "column": 0}, "end": {"line": 212, "column": 34}}, "212": {"start": {"line": 213, "column": 0}, "end": {"line": 213, "column": 120}}, "213": {"start": {"line": 214, "column": 0}, "end": {"line": 214, "column": 84}}, "214": {"start": {"line": 215, "column": 0}, "end": {"line": 215, "column": 5}}, "216": {"start": {"line": 217, "column": 0}, "end": {"line": 217, "column": 9}}, "217": {"start": {"line": 218, "column": 0}, "end": {"line": 218, "column": 77}}, "219": {"start": {"line": 220, "column": 0}, "end": {"line": 220, "column": 43}}, "220": {"start": {"line": 221, "column": 0}, "end": {"line": 221, "column": 49}}, "221": {"start": {"line": 222, "column": 0}, "end": {"line": 222, "column": 30}}, "222": {"start": {"line": 223, "column": 0}, "end": {"line": 223, "column": 34}}, "223": {"start": {"line": 224, "column": 0}, "end": {"line": 224, "column": 11}}, "224": {"start": {"line": 225, "column": 0}, "end": {"line": 225, "column": 31}}, "225": {"start": {"line": 226, "column": 0}, "end": {"line": 226, "column": 27}}, "226": {"start": {"line": 227, "column": 0}, "end": {"line": 227, "column": 8}}, "228": {"start": {"line": 229, "column": 0}, "end": {"line": 229, "column": 37}}, "229": {"start": {"line": 230, "column": 0}, "end": {"line": 230, "column": 77}}, "230": {"start": {"line": 231, "column": 0}, "end": {"line": 231, "column": 65}}, "232": {"start": {"line": 233, "column": 0}, "end": {"line": 233, "column": 47}}, "233": {"start": {"line": 234, "column": 0}, "end": {"line": 234, "column": 44}}, "234": {"start": {"line": 235, "column": 0}, "end": {"line": 235, "column": 55}}, "235": {"start": {"line": 236, "column": 0}, "end": {"line": 236, "column": 109}}, "236": {"start": {"line": 237, "column": 0}, "end": {"line": 237, "column": 9}}, "237": {"start": {"line": 238, "column": 0}, "end": {"line": 238, "column": 21}}, "238": {"start": {"line": 239, "column": 0}, "end": {"line": 239, "column": 62}}, "239": {"start": {"line": 240, "column": 0}, "end": {"line": 240, "column": 64}}, "240": {"start": {"line": 241, "column": 0}, "end": {"line": 241, "column": 5}}, "241": {"start": {"line": 242, "column": 0}, "end": {"line": 242, "column": 3}}, "244": {"start": {"line": 245, "column": 0}, "end": {"line": 245, "column": 69}}, "245": {"start": {"line": 246, "column": 0}, "end": {"line": 246, "column": 38}}, "246": {"start": {"line": 247, "column": 0}, "end": {"line": 247, "column": 18}}, "247": {"start": {"line": 248, "column": 0}, "end": {"line": 248, "column": 5}}, "250": {"start": {"line": 251, "column": 0}, "end": {"line": 251, "column": 63}}, "251": {"start": {"line": 252, "column": 0}, "end": {"line": 252, "column": 68}}, "252": {"start": {"line": 253, "column": 0}, "end": {"line": 253, "column": 73}}, "253": {"start": {"line": 254, "column": 0}, "end": {"line": 254, "column": 5}}, "255": {"start": {"line": 256, "column": 0}, "end": {"line": 256, "column": 9}}, "256": {"start": {"line": 257, "column": 0}, "end": {"line": 257, "column": 37}}, "257": {"start": {"line": 258, "column": 0}, "end": {"line": 258, "column": 76}}, "258": {"start": {"line": 259, "column": 0}, "end": {"line": 259, "column": 65}}, "260": {"start": {"line": 261, "column": 0}, "end": {"line": 261, "column": 47}}, "261": {"start": {"line": 262, "column": 0}, "end": {"line": 262, "column": 46}}, "263": {"start": {"line": 264, "column": 0}, "end": {"line": 264, "column": 41}}, "264": {"start": {"line": 265, "column": 0}, "end": {"line": 265, "column": 32}}, "265": {"start": {"line": 266, "column": 0}, "end": {"line": 266, "column": 26}}, "266": {"start": {"line": 267, "column": 0}, "end": {"line": 267, "column": 19}}, "267": {"start": {"line": 268, "column": 0}, "end": {"line": 268, "column": 11}}, "269": {"start": {"line": 270, "column": 0}, "end": {"line": 270, "column": 15}}, "270": {"start": {"line": 271, "column": 0}, "end": {"line": 271, "column": 65}}, "271": {"start": {"line": 272, "column": 0}, "end": {"line": 272, "column": 90}}, "272": {"start": {"line": 273, "column": 0}, "end": {"line": 273, "column": 47}}, "273": {"start": {"line": 274, "column": 0}, "end": {"line": 274, "column": 27}}, "274": {"start": {"line": 275, "column": 0}, "end": {"line": 275, "column": 26}}, "275": {"start": {"line": 276, "column": 0}, "end": {"line": 276, "column": 11}}, "276": {"start": {"line": 277, "column": 0}, "end": {"line": 277, "column": 10}}, "278": {"start": {"line": 279, "column": 0}, "end": {"line": 279, "column": 114}}, "279": {"start": {"line": 280, "column": 0}, "end": {"line": 280, "column": 9}}, "280": {"start": {"line": 281, "column": 0}, "end": {"line": 281, "column": 21}}, "281": {"start": {"line": 282, "column": 0}, "end": {"line": 282, "column": 63}}, "282": {"start": {"line": 283, "column": 0}, "end": {"line": 283, "column": 18}}, "283": {"start": {"line": 284, "column": 0}, "end": {"line": 284, "column": 5}}, "284": {"start": {"line": 285, "column": 0}, "end": {"line": 285, "column": 3}}, "287": {"start": {"line": 288, "column": 0}, "end": {"line": 288, "column": 113}}, "288": {"start": {"line": 289, "column": 0}, "end": {"line": 289, "column": 38}}, "289": {"start": {"line": 290, "column": 0}, "end": {"line": 290, "column": 16}}, "290": {"start": {"line": 291, "column": 0}, "end": {"line": 291, "column": 5}}, "292": {"start": {"line": 293, "column": 0}, "end": {"line": 293, "column": 9}}, "293": {"start": {"line": 294, "column": 0}, "end": {"line": 294, "column": 37}}, "294": {"start": {"line": 295, "column": 0}, "end": {"line": 295, "column": 76}}, "295": {"start": {"line": 296, "column": 0}, "end": {"line": 296, "column": 65}}, "296": {"start": {"line": 297, "column": 0}, "end": {"line": 297, "column": 46}}, "298": {"start": {"line": 299, "column": 0}, "end": {"line": 299, "column": 47}}, "299": {"start": {"line": 300, "column": 0}, "end": {"line": 300, "column": 48}}, "301": {"start": {"line": 302, "column": 0}, "end": {"line": 302, "column": 35}}, "302": {"start": {"line": 303, "column": 0}, "end": {"line": 303, "column": 82}}, "303": {"start": {"line": 304, "column": 0}, "end": {"line": 304, "column": 28}}, "304": {"start": {"line": 305, "column": 0}, "end": {"line": 305, "column": 44}}, "305": {"start": {"line": 306, "column": 0}, "end": {"line": 306, "column": 40}}, "306": {"start": {"line": 307, "column": 0}, "end": {"line": 307, "column": 14}}, "309": {"start": {"line": 310, "column": 0}, "end": {"line": 310, "column": 64}}, "310": {"start": {"line": 311, "column": 0}, "end": {"line": 311, "column": 29}}, "311": {"start": {"line": 312, "column": 0}, "end": {"line": 312, "column": 10}}, "313": {"start": {"line": 314, "column": 0}, "end": {"line": 314, "column": 106}}, "314": {"start": {"line": 315, "column": 0}, "end": {"line": 315, "column": 9}}, "315": {"start": {"line": 316, "column": 0}, "end": {"line": 316, "column": 21}}, "316": {"start": {"line": 317, "column": 0}, "end": {"line": 317, "column": 61}}, "317": {"start": {"line": 318, "column": 0}, "end": {"line": 318, "column": 16}}, "318": {"start": {"line": 319, "column": 0}, "end": {"line": 319, "column": 5}}, "319": {"start": {"line": 320, "column": 0}, "end": {"line": 320, "column": 3}}, "322": {"start": {"line": 323, "column": 0}, "end": {"line": 323, "column": 90}}, "323": {"start": {"line": 324, "column": 0}, "end": {"line": 324, "column": 38}}, "324": {"start": {"line": 325, "column": 0}, "end": {"line": 325, "column": 13}}, "325": {"start": {"line": 326, "column": 0}, "end": {"line": 326, "column": 5}}, "327": {"start": {"line": 328, "column": 0}, "end": {"line": 328, "column": 9}}, "328": {"start": {"line": 329, "column": 0}, "end": {"line": 329, "column": 60}}, "329": {"start": {"line": 330, "column": 0}, "end": {"line": 330, "column": 42}}, "330": {"start": {"line": 331, "column": 0}, "end": {"line": 331, "column": 38}}, "331": {"start": {"line": 332, "column": 0}, "end": {"line": 332, "column": 7}}, "333": {"start": {"line": 334, "column": 0}, "end": {"line": 334, "column": 50}}, "334": {"start": {"line": 335, "column": 0}, "end": {"line": 335, "column": 37}}, "335": {"start": {"line": 336, "column": 0}, "end": {"line": 336, "column": 77}}, "336": {"start": {"line": 337, "column": 0}, "end": {"line": 337, "column": 65}}, "338": {"start": {"line": 339, "column": 0}, "end": {"line": 339, "column": 40}}, "339": {"start": {"line": 340, "column": 0}, "end": {"line": 340, "column": 34}}, "340": {"start": {"line": 341, "column": 0}, "end": {"line": 341, "column": 7}}, "341": {"start": {"line": 342, "column": 0}, "end": {"line": 342, "column": 21}}, "342": {"start": {"line": 343, "column": 0}, "end": {"line": 343, "column": 52}}, "344": {"start": {"line": 345, "column": 0}, "end": {"line": 345, "column": 5}}, "345": {"start": {"line": 346, "column": 0}, "end": {"line": 346, "column": 3}}, "346": {"start": {"line": 347, "column": 0}, "end": {"line": 347, "column": 1}}}, "s": {"1": 1, "3": 1, "4": 1, "9": 1, "10": 1, "11": 1, "12": 1, "29": 1, "30": 1, "31": 5, "32": 5, "33": 5, "34": 1, "37": 0, "38": 0, "39": 0, "41": 0, "42": 0, "44": 0, "45": 0, "48": 0, "49": 0, "50": 0, "51": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "70": 0, "71": 0, "72": 0, "73": 0, "74": 0, "75": 0, "76": 0, "77": 0, "78": 0, "81": 0, "82": 0, "83": 0, "84": 0, "86": 0, "87": 0, "88": 0, "89": 0, "91": 0, "92": 0, "94": 0, "95": 0, "96": 0, "97": 0, "99": 0, "100": 0, "101": 0, "102": 0, "103": 0, "104": 0, "105": 0, "106": 0, "107": 0, "109": 0, "110": 0, "111": 0, "112": 0, "113": 0, "114": 0, "117": 0, "118": 0, "119": 0, "120": 0, "122": 0, "123": 0, "124": 0, "125": 0, "126": 0, "128": 0, "129": 0, "130": 0, "131": 0, "132": 0, "133": 0, "136": 0, "138": 0, "139": 0, "140": 0, "141": 0, "142": 0, "143": 0, "144": 0, "147": 0, "149": 0, "150": 0, "151": 0, "152": 0, "153": 0, "154": 0, "155": 0, "156": 0, "157": 0, "159": 0, "160": 0, "161": 0, "162": 0, "163": 0, "166": 0, "168": 0, "169": 0, "170": 0, "171": 0, "172": 0, "174": 0, "175": 0, "176": 0, "177": 0, "178": 0, "179": 0, "180": 0, "181": 0, "182": 0, "184": 0, "185": 0, "186": 0, "187": 0, "188": 0, "189": 0, "192": 1, "194": 1, "195": 2, "196": 2, "197": 2, "198": 2, "199": 2, "200": 0, "201": 0, "204": 2, "205": 2, "206": 1, "207": 1, "208": 1, "210": 1, "211": 1, "212": 1, "213": 1, "214": 1, "216": 0, "217": 0, "219": 0, "220": 0, "221": 0, "222": 0, "223": 0, "224": 0, "225": 0, "226": 0, "228": 0, "229": 0, "230": 0, "232": 0, "233": 0, "234": 0, "235": 0, "236": 0, "237": 0, "238": 0, "239": 0, "240": 0, "241": 2, "244": 1, "245": 2, "246": 0, "247": 0, "250": 2, "251": 2, "252": 2, "253": 2, "255": 0, "256": 0, "257": 0, "258": 0, "260": 0, "261": 0, "263": 0, "264": 0, "265": 0, "266": 0, "267": 0, "269": 0, "270": 0, "271": 0, "272": 0, "273": 0, "274": 0, "275": 0, "276": 0, "278": 0, "279": 0, "280": 0, "281": 0, "282": 0, "283": 0, "284": 2, "287": 1, "288": 0, "289": 0, "290": 0, "292": 0, "293": 0, "294": 0, "295": 0, "296": 0, "298": 0, "299": 0, "301": 0, "302": 0, "303": 0, "304": 0, "305": 0, "306": 0, "309": 0, "310": 0, "311": 0, "313": 0, "314": 0, "315": 0, "316": 0, "317": 0, "318": 0, "319": 0, "322": 1, "323": 0, "324": 0, "325": 0, "327": 0, "328": 0, "329": 0, "330": 0, "331": 0, "333": 0, "334": 0, "335": 0, "336": 0, "338": 0, "339": 0, "340": 0, "341": 0, "342": 0, "344": 0, "345": 0, "346": 1}, "branchMap": {"0": {"type": "branch", "line": 31, "loc": {"start": {"line": 31, "column": 2}, "end": {"line": 34, "column": 3}}, "locations": [{"start": {"line": 31, "column": 2}, "end": {"line": 34, "column": 3}}]}, "1": {"type": "branch", "line": 195, "loc": {"start": {"line": 195, "column": 2}, "end": {"line": 242, "column": 3}}, "locations": [{"start": {"line": 195, "column": 2}, "end": {"line": 242, "column": 3}}]}, "2": {"type": "branch", "line": 200, "loc": {"start": {"line": 200, "column": 37}, "end": {"line": 202, "column": 5}}, "locations": [{"start": {"line": 200, "column": 37}, "end": {"line": 202, "column": 5}}]}, "3": {"type": "branch", "line": 206, "loc": {"start": {"line": 206, "column": 38}, "end": {"line": 215, "column": 5}}, "locations": [{"start": {"line": 206, "column": 38}, "end": {"line": 215, "column": 5}}]}, "4": {"type": "branch", "line": 215, "loc": {"start": {"line": 215, "column": 4}, "end": {"line": 241, "column": 5}}, "locations": [{"start": {"line": 215, "column": 4}, "end": {"line": 241, "column": 5}}]}, "5": {"type": "branch", "line": 245, "loc": {"start": {"line": 245, "column": 2}, "end": {"line": 285, "column": 3}}, "locations": [{"start": {"line": 245, "column": 2}, "end": {"line": 285, "column": 3}}]}, "6": {"type": "branch", "line": 246, "loc": {"start": {"line": 246, "column": 37}, "end": {"line": 248, "column": 5}}, "locations": [{"start": {"line": 246, "column": 37}, "end": {"line": 248, "column": 5}}]}, "7": {"type": "branch", "line": 254, "loc": {"start": {"line": 254, "column": 4}, "end": {"line": 284, "column": 5}}, "locations": [{"start": {"line": 254, "column": 4}, "end": {"line": 284, "column": 5}}]}}, "b": {"0": [5], "1": [2], "2": [0], "3": [1], "4": [0], "5": [2], "6": [0], "7": [0]}, "fnMap": {"0": {"name": "VaultError", "decl": {"start": {"line": 31, "column": 2}, "end": {"line": 34, "column": 3}}, "loc": {"start": {"line": 31, "column": 2}, "end": {"line": 34, "column": 3}}, "line": 31}, "1": {"name": "openVaultDB", "decl": {"start": {"line": 38, "column": 0}, "end": {"line": 63, "column": 1}}, "loc": {"start": {"line": 38, "column": 0}, "end": {"line": 63, "column": 1}}, "line": 38}, "2": {"name": "generateDeviceKey", "decl": {"start": {"line": 66, "column": 0}, "end": {"line": 79, "column": 1}}, "loc": {"start": {"line": 66, "column": 0}, "end": {"line": 79, "column": 1}}, "line": 66}, "3": {"name": "getDeviceKey", "decl": {"start": {"line": 82, "column": 0}, "end": {"line": 115, "column": 1}}, "loc": {"start": {"line": 82, "column": 0}, "end": {"line": 115, "column": 1}}, "line": 82}, "4": {"name": "storeDeviceKey", "decl": {"start": {"line": 118, "column": 0}, "end": {"line": 134, "column": 1}}, "loc": {"start": {"line": 118, "column": 0}, "end": {"line": 134, "column": 1}}, "line": 118}, "5": {"name": "encryptData", "decl": {"start": {"line": 137, "column": 0}, "end": {"line": 164, "column": 1}}, "loc": {"start": {"line": 137, "column": 0}, "end": {"line": 164, "column": 1}}, "line": 137}, "6": {"name": "decryptData", "decl": {"start": {"line": 167, "column": 0}, "end": {"line": 190, "column": 1}}, "loc": {"start": {"line": 167, "column": 0}, "end": {"line": 190, "column": 1}}, "line": 167}, "7": {"name": "createSnapshot", "decl": {"start": {"line": 195, "column": 2}, "end": {"line": 242, "column": 3}}, "loc": {"start": {"line": 195, "column": 2}, "end": {"line": 242, "column": 3}}, "line": 195}, "8": {"name": "getSnapshot", "decl": {"start": {"line": 245, "column": 2}, "end": {"line": 285, "column": 3}}, "loc": {"start": {"line": 245, "column": 2}, "end": {"line": 285, "column": 3}}, "line": 245}, "9": {"name": "listSnapshots", "decl": {"start": {"line": 288, "column": 2}, "end": {"line": 320, "column": 3}}, "loc": {"start": {"line": 288, "column": 2}, "end": {"line": 320, "column": 3}}, "line": 288}, "10": {"name": "cleanupSnapshots", "decl": {"start": {"line": 323, "column": 2}, "end": {"line": 346, "column": 3}}, "loc": {"start": {"line": 323, "column": 2}, "end": {"line": 346, "column": 3}}, "line": 323}}, "f": {"0": 5, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 2, "8": 2, "9": 0, "10": 0}}}